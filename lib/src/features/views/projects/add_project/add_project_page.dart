import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/mytoast.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/other_settings_bloc.dart';
import '../../../models/category_model.dart';
import '../../../models/developer_model.dart';
import '../../../models/floor_plan_model.dart';
import '../../../models/other_settings.dart';
import '../../../models/price_plan_model.dart';
import '../../../models/project_plan_model.dart';
import '../../../models/property_status_model.dart';
import '../../../repository/project_repository.dart';
import '../projects_page.dart';
import 'widgets/basic_information_section.dart';
import 'widgets/descriptions_section.dart';
import 'widgets/floor_plans_section.dart';
import 'widgets/gallery_images_section.dart';
import 'widgets/location_section.dart';
import 'widgets/project_plans_section.dart';
import 'widgets/rera_permit_section.dart';

class AddProjectPage extends HookWidget {
  final CategoryModel? project; // For editing existing projects

  const AddProjectPage({super.key, this.project});

  @override
  Widget build(BuildContext context) {
    // Controllers for basic information
    final nameArController = useTextEditingController();
    final nameEnController = useTextEditingController();
    final publishDateController = useTextEditingController();

    // Description states (using ValueNotifier for quill editor)
    final arabicDescription = useState<String?>(null);
    final englishDescription = useState<String?>(null);

    // Selection states
    final selectedType = useState<OtherSettingsModel?>(null);
    final selectedPropertyStatus = useState<PropertyStatusModel?>(null);
    final selectedPricePlan = useState<PricePlanModel?>(null);
    final selectedPaymentMethod = useState<String?>('cash'); // Default to cash
    final selectedLocation = useState<OtherSettingsModel?>(null);
    final selectedDeveloper = useState<DeveloperModel?>(null);

    // Location states
    final markers = useState<Set<Marker>?>(null);

    // Project plans and floor plans
    final projectPlans = useState<List<ProjectPlanModel>>([
      ProjectPlanModel(id: DateTime.now().millisecondsSinceEpoch.toString())
    ]);
    final floorPlans = useState<List<FloorPlanModel>>(
        [FloorPlanModel(id: DateTime.now().millisecondsSinceEpoch.toString())]);

    // Featured settings
    final featuredHome = useState<bool>(false);
    final featuredCategory = useState<bool>(false);

    // Gallery images and videos
    final images = useState<List<File>?>(null);
    final mainVideo = useState<File?>(null);
    final mainVideoAr = useState<File?>(null);

    // RERA Permit
    final reraNumberController = useTextEditingController();
    final reraPermitImage = useState<File?>(null);

    // Loading state
    final isLoading = useState<bool>(false);

    // Repository
    final projectRepository = ProjectRepository();

    // Initialize locations on page load and populate fields if editing
    useEffect(() {
      othersettingsbloc.getLocations();

      // If editing, populate the form fields
      if (project != null) {
        nameArController.text = project!.name ?? '';
        nameEnController.text = project!.name ?? '';
        publishDateController.text = project!.publishDate ?? '';
        arabicDescription.value = project!.description;
        englishDescription.value = project!.description;
        featuredHome.value = project!.featuredHome == 1;
        featuredCategory.value = project!.featuredCategory == 1;

        // Set location marker if available
        if (project!.lat != null && project!.lng != null) {
          markers.value = {
            Marker(
              markerId: const MarkerId('selected_location'),
              position: LatLng(project!.lat!, project!.lng!),
            ),
          };
        }
      }

      return null;
    }, []);

    return SafeArea(
      top: false,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(
            project != null
                ? S.of(context).EditProject
                : S.of(context).addNewProject,
            style: const TextStyle(color: Colors.white),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).Basicinformation,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: true,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: BasicInformationSection(
                          nameArController: nameArController,
                          nameEnController: nameEnController,
                          publishDateController: publishDateController,
                          selectedType: selectedType,
                          selectedPropertyStatus: selectedPropertyStatus,
                          selectedPricePlan: selectedPricePlan,
                          selectedPaymentMethod: selectedPaymentMethod,
                          selectedDeveloper: selectedDeveloper,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Descriptions Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).descriptions,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: DescriptionsSection(
                          arabicDescription: arabicDescription,
                          englishDescription: englishDescription,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Location Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).Locationation,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: LocationSection(
                          markers: markers,
                          selectedLocation: selectedLocation,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Project Plans Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).availableUnits,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ProjectPlansSection(
                          projectPlans: projectPlans,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Floor Plans Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).floorPlans,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: FloorPlansSection(
                          floorPlans: floorPlans,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 10),

                // Gallery Images Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).galleryImages,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: GalleryImagesSection(
                          images: images,
                          mainVideo: mainVideo,
                          mainVideoAr: mainVideoAr,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // RERA Permit Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).reraPermit,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ReraPermitSection(
                          reraNumberController: reraNumberController,
                          reraPermitImage: reraPermitImage,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 40),

                // Submit Button
                !isLoading.value
                    ? SizedBox(
                        height: 60,
                        width: MediaQuery.of(context).size.width,
                        child: ElevatedButton(
                          onPressed: () => _submitProject(
                            context,
                            nameArController,
                            nameEnController,
                            publishDateController,
                            arabicDescription,
                            englishDescription,
                            selectedType,
                            selectedPropertyStatus,
                            selectedPricePlan,
                            selectedPaymentMethod,
                            selectedLocation,
                            selectedDeveloper,
                            markers,
                            projectPlans,
                            floorPlans,
                            featuredHome,
                            featuredCategory,
                            images,
                            mainVideo,
                            mainVideoAr,
                            reraNumberController,
                            reraPermitImage,
                            isLoading,
                            projectRepository,
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: GlobalColors.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: Text(
                            S.of(context).addProject,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                    : const ADLinearProgressIndicator(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _submitProject(
    BuildContext context,
    TextEditingController nameArController,
    TextEditingController nameEnController,
    TextEditingController publishDateController,
    ValueNotifier<String?> arabicDescription,
    ValueNotifier<String?> englishDescription,
    ValueNotifier<OtherSettingsModel?> selectedType,
    ValueNotifier<PropertyStatusModel?> selectedPropertyStatus,
    ValueNotifier<PricePlanModel?> selectedPricePlan,
    ValueNotifier<String?> selectedPaymentMethod,
    ValueNotifier<OtherSettingsModel?> selectedLocation,
    ValueNotifier<DeveloperModel?> selectedDeveloper,
    ValueNotifier<Set<Marker>?> markers,
    ValueNotifier<List<ProjectPlanModel>> projectPlans,
    ValueNotifier<List<FloorPlanModel>> floorPlans,
    ValueNotifier<bool> featuredHome,
    ValueNotifier<bool> featuredCategory,
    ValueNotifier<List<File>?> images,
    ValueNotifier<File?> mainVideo,
    ValueNotifier<File?> mainVideoAr,
    TextEditingController reraNumberController,
    ValueNotifier<File?> reraPermitImage,
    ValueNotifier<bool> isLoading,
    ProjectRepository projectRepository,
  ) async {
    // Validation
    if (nameArController.text.isEmpty) {
      snackbar(S.of(context).pleaseEnterArabicName);
      return;
    }
    if (nameEnController.text.isEmpty) {
      snackbar(S.of(context).pleaseEnterEnglishName);
      return;
    }
    if (arabicDescription.value?.isEmpty ?? true) {
      snackbar(S.of(context).pleaseEnterArabicDescription);
      return;
    }
    if (englishDescription.value?.isEmpty ?? true) {
      snackbar(S.of(context).pleaseEnterEnglishDescription);
      return;
    }
    if (selectedType.value == null) {
      snackbar(S.of(context).pleaseSelectType);
      return;
    }
    if (selectedPropertyStatus.value == null) {
      snackbar(S.of(context).pleaseSelectPropertyStatus);
      return;
    }
    if (selectedPaymentMethod.value == null) {
      snackbar(S.of(context).selectPaymentMethod);
      return;
    }
    if (selectedPaymentMethod.value == 'installment' &&
        selectedPricePlan.value == null) {
      snackbar(S.of(context).pleaseSelectPricePlan);
      return;
    }
    if (selectedLocation.value == null) {
      snackbar(S.of(context).pleaseSelectLocation);
      return;
    }
    if (markers.value == null || markers.value!.isEmpty) {
      snackbar(S.of(context).pleaseSelectLocation);
      return;
    }

    isLoading.value = true;

    try {
      // Prepare form data
      final Map<String, dynamic> formDataMap = {
        'name[ar]': nameArController.text,
        'name[en]': nameEnController.text,
        'description[ar]': arabicDescription.value!,
        'description[en]': englishDescription.value!,
        'category_id': 10, // Properties category
        'payment_method': selectedPaymentMethod.value!,
        'location_id': selectedLocation.value!.id,
        'latitude': markers.value!.first.position.latitude,
        'longitude': markers.value!.first.position.longitude,
        'featuredHome': featuredHome.value ? 1 : 0,
        'featuredCategory': featuredCategory.value ? 1 : 0,
        'type_id': selectedType.value!.id,
        'property_status': selectedPropertyStatus.value!.id,
        'publish_date': publishDateController.text,
      };

      // Add developer if selected
      if (selectedDeveloper.value != null) {
        formDataMap['developer_id'] = selectedDeveloper.value!.id;
      }

      // Add price plan only if installment is selected
      if (selectedPaymentMethod.value == 'installment' &&
          selectedPricePlan.value != null) {
        formDataMap['price_plan_id'] = selectedPricePlan.value!.id;
      }

      // Add RERA permit data
      if (reraNumberController.text.isNotEmpty) {
        formDataMap['rera_permits[0][rera_number]'] = reraNumberController.text;
      }

      final formData = FormData.fromMap(formDataMap);

      // Add project plans
      for (int i = 0; i < projectPlans.value.length; i++) {
        final plan = projectPlans.value[i];
        if (plan.bedroomsAr?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][bedrooms][ar]', plan.bedroomsAr!));
        }
        if (plan.bedroomsEn?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][bedrooms][en]', plan.bedroomsEn!));
        }
        if (plan.priceFrom?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('project_plans[$i][price_from]', plan.priceFrom!));
        }
        if (plan.priceTo?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('project_plans[$i][price_to]', plan.priceTo!));
        }
        if (plan.spaceSizeAr?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][space_size][ar]', plan.spaceSizeAr!));
        }
        if (plan.spaceSizeEn?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][space_size][en]', plan.spaceSizeEn!));
        }
      }

      // Add floor plans
      for (int i = 0; i < floorPlans.value.length; i++) {
        final plan = floorPlans.value[i];
        if (plan.nameAr?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('floor_plans[$i][name][ar]', plan.nameAr!));
        }
        if (plan.nameEn?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('floor_plans[$i][name][en]', plan.nameEn!));
        }
        // Add floor plan image
        if (plan.image != null) {
          formData.files.add(MapEntry(
            'floor_plans[$i][image]',
            await MultipartFile.fromFile(plan.image!.path),
          ));
        }
      }

      // Add gallery images
      if (images.value != null) {
        for (int i = 0; i < images.value!.length; i++) {
          formData.files.add(MapEntry(
            "image[]",
            await MultipartFile.fromFile(images.value![i].path),
          ));
        }
      }

      // Add main video
      if (mainVideo.value != null) {
        formData.files.add(MapEntry(
          "video",
          await MultipartFile.fromFile(mainVideo.value!.path),
        ));
      }

      // Add main video Arabic
      if (mainVideoAr.value != null) {
        formData.files.add(MapEntry(
          "video_ar",
          await MultipartFile.fromFile(mainVideoAr.value!.path),
        ));
      }

      // Add RERA permit image
      if (reraPermitImage.value != null) {
        formData.files.add(MapEntry(
          "rera_permits[0][rera_permit_image]",
          await MultipartFile.fromFile(reraPermitImage.value!.path),
        ));
      }

      // Submit to API
      final response = await projectRepository.addProject(formData);

      if (response.code == 1) {
        MyToast.showSuccess(text: S.of(context).projectAddedSuccessfully);
        Navigator.pop(context);
        Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => const ProjectsPage(),
            ));
      } else {
        snackbar(response.msg ?? S.of(context).failedToAddProject);
      }
    } catch (e) {
      snackbar('Error: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
