import 'dart:developer';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../models/floor_plan_model.dart';
import '../../../../models/project_models.dart' as project_models;

class FloorPlansSection extends HookWidget {
  final ValueNotifier<List<FloorPlanModel>> floorPlans;
  final List<project_models.FloorPlanModel>? existingFloorPlans;

  const FloorPlansSection({
    super.key,
    required this.floorPlans,
    this.existingFloorPlans,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Floor Plans List with Drag and Drop
        ...floorPlans.value.asMap().entries.map((entry) {
          final index = entry.key;
          final plan = entry.value;
          return _buildDraggableFloorPlanItem(context, index, plan);
        }).toList(),

        const SizedBox(height: 10),

        // Add New Floor Plan Button
        SizedBox(
          height: 45,
          width: MediaQuery.of(context).size.width,
          child: ElevatedButton.icon(
            onPressed: () {
              floorPlans.value = [
                ...floorPlans.value,
                FloorPlanModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                ),
              ];
            },
            icon: const Icon(Icons.add, size: 16, color: Colors.white),
            label: Text(
              S.of(context).addFloorPlan,
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: GlobalColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDraggableFloorPlanItem(
      BuildContext context, int index, FloorPlanModel plan) {
    return DragTarget<FloorPlanModel>(
      builder: (context, candidateData, rejectedData) {
        return Draggable<FloorPlanModel>(
          data: plan,
          feedback: Material(
            elevation: 4.0,
            child: SizedBox(
              width: MediaQuery.of(context).size.width - 40,
              child: _buildFloorPlanItem(context, index, plan),
            ),
          ),
          childWhenDragging: Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(10),
              color: Colors.grey[100],
            ),
            child: SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'Moving ${S.of(context).floorPlan} ${index + 1}...',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ),
          ),
          child: _buildFloorPlanItem(context, index, plan),
        );
      },
      onWillAccept: (data) => data != plan,
      onAccept: (draggedPlan) {
        final draggedIndex = floorPlans.value.indexOf(draggedPlan);
        final targetIndex = index;

        if (draggedIndex != -1 && draggedIndex != targetIndex) {
          final newList = List<FloorPlanModel>.from(floorPlans.value);
          final item = newList.removeAt(draggedIndex);
          newList.insert(targetIndex, item);
          floorPlans.value = newList;
        }
      },
    );
  }

  Widget _buildFloorPlanItem(
      BuildContext context, int index, FloorPlanModel plan) {
    return HookBuilder(
      key: ValueKey(plan.id), // Use plan ID as key to maintain state
      builder: (context) {
        final nameArController =
            useTextEditingController(text: plan.nameAr ?? '');
        final nameEnController =
            useTextEditingController(text: plan.nameEn ?? '');
        // Initialize selectedImage with current plan's image
        final selectedImage = useState<File?>(plan.image);
        log('asfsafsa $selectedImage');

        // Sync controllers with plan data when plan changes
        useEffect(() {
          if (nameArController.text != (plan.nameAr ?? '')) {
            nameArController.text = plan.nameAr ?? '';
          }
          if (nameEnController.text != (plan.nameEn ?? '')) {
            nameEnController.text = plan.nameEn ?? '';
          }
          selectedImage.value = plan.image;
          return null;
        }, [plan.nameAr, plan.nameEn, plan.image]);

        // Update the plan when controllers change
        useEffect(() {
          void updatePlan() {
            final currentIndex =
                floorPlans.value.indexWhere((p) => p.id == plan.id);
            if (currentIndex != -1) {
              final updatedPlan = plan.copyWith(
                nameAr: nameArController.text,
                nameEn: nameEnController.text,
                image: selectedImage.value,
              );
              final newList = List<FloorPlanModel>.from(floorPlans.value);
              newList[currentIndex] = updatedPlan;
              floorPlans.value = newList;
            }
          }

          nameArController.addListener(updatePlan);
          nameEnController.addListener(updatePlan);
          return () {
            nameArController.removeListener(updatePlan);
            nameEnController.removeListener(updatePlan);
          };
        }, [plan.id]);

        return Container(
          margin: const EdgeInsets.only(bottom: 15),
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and delete button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.drag_handle, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(
                        '${S.of(context).floorPlan} ${index + 1}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  if (floorPlans.value.length > 1)
                    IconButton(
                      onPressed: () {
                        final newList =
                            List<FloorPlanModel>.from(floorPlans.value);
                        newList.removeWhere((p) => p.id == plan.id);
                        floorPlans.value = newList;
                      },
                      icon: const Icon(Icons.delete, color: Colors.red),
                    ),
                ],
              ),
              const SizedBox(height: 10),

              // Name Arabic
              Text(
                S.of(context).arabicName,
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 5),
              Container(
                height: 45,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(width: 0.5, color: Colors.grey[300]!),
                  color: Colors.white,
                ),
                child: TextFormField(
                  controller: nameArController,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                    hintText: S.of(context).enterArabicName,
                    hintStyle: const TextStyle(
                      color: Color(0xffB7B7B7),
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),

              // Name English
              Text(
                S.of(context).englishName,
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 5),
              Container(
                height: 45,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(width: 0.5, color: Colors.grey[300]!),
                  color: Colors.white,
                ),
                child: TextFormField(
                  controller: nameEnController,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                    hintText: S.of(context).enterEnglishName,
                    hintStyle: const TextStyle(
                      color: Color(0xffB7B7B7),
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),

              // Floor Plan Image
              Text(
                S.of(context).floorPlanImage,
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 5),

              // Show existing floor plan images if available
              if (existingFloorPlans != null && existingFloorPlans!.isNotEmpty)
                Builder(
                  builder: (context) {
                    // Find the corresponding existing floor plan by matching names or index
                    final currentIndex = floorPlans.value.indexWhere((p) => p.id == plan.id);
                    if (currentIndex >= 0 && currentIndex < existingFloorPlans!.length) {
                      final existingPlan = existingFloorPlans![currentIndex];
                      if (existingPlan.media != null && existingPlan.media!.isNotEmpty) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              S.of(context).existingFloorPlanImages,
                              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              height: 80,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: existingPlan.media!.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.grey[300]!),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        existingPlan.media![index].url ?? '',
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Container(
                                            color: Colors.grey[200],
                                            child: const Icon(Icons.error, color: Colors.grey),
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 10),
                          ],
                        );
                      }
                    }
                    return const SizedBox.shrink();
                  },
                ),

              ADFilePicker(
                addedFile: selectedImage.value != null
                    ? File(selectedImage.value!.path)
                    : null,
                onSingleFileSelected: (image) {
                  selectedImage.value = image;
                  final currentIndex =
                      floorPlans.value.indexWhere((p) => p.id == plan.id);
                  if (currentIndex != -1) {
                    final updatedPlan = plan.copyWith(image: image);
                    final newList = List<FloorPlanModel>.from(floorPlans.value);
                    newList[currentIndex] = updatedPlan;
                    floorPlans.value = newList;
                  }
                },
                title: S.of(context).Tabheretouploadimage,
                type: FileType.media,
                isMultiple: false,
              ),
            ],
          ),
        );
      },
    );
  }
}
