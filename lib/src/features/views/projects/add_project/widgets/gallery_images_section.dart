import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_file_picker.dart';

class GalleryImagesSection extends HookWidget {
  final ValueNotifier<List<File>?> images;
  final ValueNotifier<File?> mainVideo;
  final ValueNotifier<File?> mainVideoAr;
  final List<String>? existingImages;
  final String? existingMainVideo;
  final String? existingMainVideoAr;

  const GalleryImagesSection({
    super.key,
    required this.images,
    required this.mainVideo,
    required this.mainVideoAr,
    this.existingImages,
    this.existingMainVideo,
    this.existingMainVideoAr,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Gallery Images Label
        Text(
          S.of(context).UploadImage,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),

        // File Picker for Multiple Images
        ADFilePicker(
          onFilesSelected: (selectedImages) {
            images.value = selectedImages;
          },
          title: S.of(context).Tabheretouploadimage,
          type: FileType.media,
          isMultiple: true,
        ),
        const SizedBox(height: 10),

        // Display existing images
        if (existingImages != null && existingImages!.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).existingImages,
                style:
                    const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Container(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: existingImages!.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          existingImages![index],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child:
                                  const Icon(Icons.error, color: Colors.grey),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 10),
            ],
          ),

        // Display selected images count
        if (images.value != null && images.value!.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.blue[50],
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.image, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '${images.value!.length} ${images.value!.length == 1 ? S.of(context).imageSelected : S.of(context).imagesSelected}',
                  style: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 20),

        // Main Video Upload
        Text(
          S.of(context).UploadMainVideo,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),

        // Show existing main video if available
        if (existingMainVideo != null && existingMainVideo!.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.green[50],
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.video_file, color: Colors.green),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    S.of(context).existingMainVideo,
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

        ADFilePicker(
          title: S.of(context).Tabheretouploadmainvideo,
          onSingleFileSelected: (video) => mainVideo.value = video,
          isMultiple: false,
          type: FileType.video,
        ),
        const SizedBox(height: 20),

        // Main Video Arabic Upload
        Text(
          S.of(context).UploadMainVideoAr,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),

        // Show existing main video Arabic if available
        if (existingMainVideoAr != null && existingMainVideoAr!.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.green[50],
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.video_file, color: Colors.green),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    S.of(context).existingMainVideoAr,
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

        ADFilePicker(
          title: S.of(context).TabheretouploadmainvideoAr,
          onSingleFileSelected: (video) => mainVideoAr.value = video,
          isMultiple: false,
          type: FileType.video,
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
