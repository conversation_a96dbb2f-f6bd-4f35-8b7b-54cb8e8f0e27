import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:admin_dubai/src/features/response/category_response.dart';
import 'package:admin_dubai/src/features/views/projects/add_project/add_project_page.dart';
import 'package:admin_dubai/src/features/views/projects/widgets/delete_project.dart';
import 'package:admin_dubai/src/features/views/projects/widgets/project_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../core/shared_widgets/types_search_sheet.dart';
import '../../../core/utils/resources.dart';
import '../../models/other_settings.dart';

class ProjectsPage extends HookWidget {
  const ProjectsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final searchController = useTextEditingController();
    final selectedType = useState<OtherSettingsModel?>(null);
    final isLoading = useState(false);

    // Load initial data
    useEffect(() {
      categoryBloc.getCategories(AppConstants.projectsId.toString(), 0, 200, '',
          selectedType.value?.id?.toString());
      return null;
    }, [selectedType.value]);

    void performSearch([String? searchText]) {
      categoryBloc.getCategories(
        AppConstants.projectsId.toString(),
        0,
        200,
        searchText ?? searchController.text,
        selectedType.value?.id?.toString(),
      );
    }

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).Projects),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddProjectPage(),
                  ),
                );
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // Search bar
            Container(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: searchController,
                  textInputAction: TextInputAction.search,
                  onFieldSubmitted: (value) {
                    performSearch(value);
                  },
                  decoration: InputDecoration(
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Color(0xff8B959E),
                    ),
                    contentPadding:
                        const EdgeInsets.only(left: 20, right: 20, top: 5),
                    hintText: S.of(context).SearchProjects,
                    hintStyle:
                        const TextStyle(color: Color(0xff8B959E), fontSize: 13),
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            // Type filter
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TypesSearchSheet(
                label: S.of(context).Type,
                selectedValue: selectedType.value,
                onChanged: (value) {
                  selectedType.value = value;
                },
                category: '10',
              ),
            ),
            const SizedBox(height: 16),
            // Projects list
            Expanded(
              child: StreamBuilder<CategoryResponse?>(
                stream: categoryBloc.subject.stream,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: ADCircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text(S.of(context).ErrorLoadingProjects),
                    );
                  }

                  if (!snapshot.hasData || snapshot.data!.category.isEmpty) {
                    return Center(
                      child: Text(S.of(context).NoProjectsFound),
                    );
                  }

                  final projects = snapshot.data!.category;

                  return ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: projects.length,
                    itemBuilder: (context, index) {
                      final project = projects[index];
                      return ProjectCard(
                        project: project,
                        onEdit: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AddProjectPage(
                                project: project,
                              ),
                            ),
                          );
                        },
                        onDelete: () {
                          deleteProject(
                            project.id,
                            index,
                            context: context,
                            isLoading: isLoading,
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
