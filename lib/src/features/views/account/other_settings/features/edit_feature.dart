import 'dart:developer';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/other_settings_api_provider.dart';
import '../../../../bloc/category_bloc.dart';
import '../../../../response/generalResponse.dart';

// ignore: must_be_immutable
class EditFeature extends StatefulWidget {
  String? name;
  MainCategoryModel? currentvalue2;
  int? id;
  EditFeature(this.name, this.currentvalue2, this.id, {super.key});
  @override
  _EditFeature createState() => _EditFeature();
}

class _EditFeature extends State<EditFeature> {
  // List<CategoryModel> category = [
  // 'hotels',
  // 'holidayHomes',
  // 'carRent',
  // 'restaurants',
  // 'shops',
  // 'activities',
  // 'luxury',
  // 'places'
  // ];
  MainCategoryModel? selectedCategory;
  bool isactions = false;
  @override
  void initState() {
    selectedCategory = widget.currentvalue2!;

    log('currentvalue2 ${selectedCategory?.toJson()}');

    categoryBloc.getMainCategories();

    getFeature();
    super.initState();
  }

  TextEditingController namearController = TextEditingController();
  TextEditingController nameenController = TextEditingController();
  OtherSettingsApiProvider provider = OtherSettingsApiProvider();
  bool isLoading = false;
  bool isget = false;
  void getFeature() async {
    await provider.getfeaturebyid(widget.id!).then((value) {
      // log('Daataa ${value?.results[0]['name']}');
      value != null
          ? setState(() {
              namearController.text = value.results[0]['name_ar'] ?? "";
              nameenController.text = value.results[0]['name_en'] ?? "";
              isget = true;
            })
          : null;
    });
  }

  submit(FormData data) async {
    if (namearController.text == "") {
      snackbar(S.of(context).enteraran);

      return;
    }
    if (nameenController.text == "") {
      snackbar(S.of(context).enterenn);

      return;
    }

    setState(() {
      isLoading = true;
    });

    // pr.show();
    final GeneralResponse successInformation = await provider.editfeature(data);
    if (successInformation.code == 1) {
      Navigator.pop(context, true);
    } else {
      if (successInformation.msg == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation.msg!);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(widget.name!),
              actions: [
                Container(
                    padding: const EdgeInsets.only(left: 10, right: 10),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          isactions = !isactions;
                        });
                      },
                      child: const Icon(Icons.more_horiz, color: Colors.white),
                    ))
              ],
            ),
            body: Stack(children: [
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context).feaen,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: nameenController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).feaen,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).feaar,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: namearController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).feaar,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const Spacer(),
                    !isLoading
                        ? Container(
                            height: 60,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Center(
                                    child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                            onTap: () async {
                                              FormData form = FormData.fromMap({
                                                "id": widget.id,
                                                'name[en]':
                                                    nameenController.text,
                                                "name[ar]":
                                                    namearController.text,
                                                "category": 8,
                                              });
                                              submit(form);
                                            },
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      GlobalColors.primaryColor,
                                                  borderRadius:
                                                      BorderRadius.circular(5)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    S.of(context).SaveChanges,
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            ))))
                              ],
                            ))
                        : const ADLinearProgressIndicator()
                  ],
                ),
              ),
              isactions == true
                  ? Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      color: Colors.grey.withOpacity(0.4),
                      child: Container(
                          padding: const EdgeInsets.only(
                            left: 10,
                            right: 10,
                            bottom: 20,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.grey[100],
                                ),
                                width: MediaQuery.of(context).size.width,
                                child: Column(
                                  children: [
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    GestureDetector(
                                        onTap: () {
                                          deletefeature();
                                        },
                                        child: Text(
                                          S.of(context).Deletefeature,
                                          style: const TextStyle(
                                              color: Color(0xffE04E4D),
                                              fontSize: 18),
                                        )),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isactions = !isactions;
                                    });
                                  },
                                  child: Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: Colors.white,
                                    ),
                                    width: MediaQuery.of(context).size.width,
                                    child: Center(
                                      child: Text(
                                        S.of(context).Cancel,
                                        style: const TextStyle(
                                            color: Color(0xff007AFF),
                                            fontSize: 18),
                                      ),
                                    ),
                                  ))
                            ],
                          )),
                    )
                  : Container()
            ])));
  }

  void deletefeature() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        S.of(context).Deletefeature,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(S
                                    .of(context)
                                    .Areyousureyouwanttodeletethisfeature),
                                const SizedBox(
                                  height: 20,
                                ),
                                Center(
                                    child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                            onTap: () async {
                                              GeneralResponse
                                                  successinformation =
                                                  await provider.deletefeature(
                                                      widget.id!);
                                              if (successinformation.code ==
                                                  1) {
                                                Navigator.pop(context);

                                                Navigator.pop(context, true);
                                              } else {
                                                if (successinformation.msg ==
                                                    null) {
                                                  Navigator.pop(context, true);

                                                  snackbar(S
                                                      .of(context)
                                                      .cannotDeleteBecauseItIsUsedInVideo);
                                                } else {
                                                  Navigator.pop(context, true);

                                                  snackbar(
                                                    S
                                                        .of(context)
                                                        .cannotDeleteBecauseItIsUsedInVideo,
                                                  );
                                                }
                                              }
                                              // _submit(rate.toString(), _comment.text);
                                            },
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xffE04E4D),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    S.of(context).yesde,
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            )))),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
            ));
  }
}
