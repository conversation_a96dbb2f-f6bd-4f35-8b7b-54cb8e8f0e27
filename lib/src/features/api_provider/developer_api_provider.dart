import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/developer_response.dart';
import '../response/generalResponse.dart';

class DeveloperApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<DeveloperResponse> getDevelopers(
      [int? page, int? size, String? key]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      String url;
      if (page != null) {
        url = '${_endpoint}developers?page=$page&size=$size&key=$key';
      } else {
        url = '${_endpoint}developers';
      }
      print(url);

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("developers response");
      print(response);
      return DeveloperResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return DeveloperResponse.withError(handleError(error));
    }
  }

  Future<DeveloperDetailsResponse> getDeveloperById(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      String url = '${_endpoint}developers/$id';
      print(url);

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("developer details response");
      print(response);
      return DeveloperDetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return DeveloperDetailsResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addDeveloper(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("add developer");
      print("Token: $token");
      print("Endpoint: ${_endpoint}add-developer");

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print('FormData ${data.fields.map((e) => '${e.key}: ${e.value}')}');

      Response response = await _dio.post('${_endpoint}add-developer',
          data: data,
          options: Options(
              headers: {"Content-Type": "multipart/form-data"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("add developer response URL: ${_endpoint}add-developer");
      print("Response status: ${response.statusCode}");
      print("Response headers: ${response.headers}");
      print("Response data: ${response.data}");

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> updateDeveloper(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("update developer");
      print("Token: $token");
      print("Endpoint: ${_endpoint}update-developer");

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print('FormData ${data.fields.map((e) => '${e.key}: ${e.value}')}');

      Response response = await _dio.post('${_endpoint}update-developer',
          data: data,
          options: Options(
              headers: {"Content-Type": "multipart/form-data"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("update developer response URL: ${_endpoint}developers/update");
      print("Response status: ${response.statusCode}");
      print("Response headers: ${response.headers}");
      print("Response data: ${response.data}");

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteDeveloper(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("delete developer");
      print("Token: $token");
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      Response response = await _dio.post('${_endpoint}delete-developer',
          data: {"id": id},
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("delete developer response");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
