import 'dart:developer';

import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../models/main_category_model.dart';
import '../response/category_reels_response.dart';
import '../response/category_response.dart';
import '../response/generalResponse.dart';

class CategoryApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<CategoryResponse> getCategories(String category,
      [int? page, int? size, String? key = '', String? type]) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var token = prefs.getString('token');
      var userid = prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url =
          '${_endpoint}getmaincategory?${category.isNotEmpty ? 'category=$category&' : ''}page=$page&size=$size&key=${key ?? ''}${type != null && type.isNotEmpty ? '&type=$type' : ''}';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq ${response.data}");
      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }

  Future<CategoryDetailsResponse> getMainCategoryDetails(
      {int? id, String? categoryName}) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var token = prefs.getString('token');
      var userid = prefs.getInt('user_id');
      print("token = $token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      switch (categoryName) {
        case "HolidayHomes":
          url = '${_endpoint}getmaincategory?id=$id';
          break;
        case "CarRents":
          url = '${_endpoint}getmaincategory?id=$id';
          break;
        case "Properties":
          url = '${_endpoint}getmaincategory?id=$id';
          break;
        default:
          url = '${_endpoint}getmaincategory?id=$id';
      }

      Response response = await _dio.get(
        url,
        options: Options(validateStatus: (status) {
          return status! < 600;
        }),
      );
      print(url);
      return CategoryDetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CategoryDetailsResponse.withError(handleError(error));
    }
  }

  // Future<CategoryImagesResponse> getMainCategoryImages(
  //     int id, String? categoryName) async {
  //   try {
  //     SharedPreferences prefs = await SharedPreferences.getInstance();
  //     var token = prefs.getString('token');
  //     var userid = prefs.getInt('user_id');
  //     print("token");
  //     print(userid);
  //     _dio.options.headers['Authorization'] = 'Bearer ${token!}';
  //     _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
  //     String url;
  //     print(
  //         "=========================== $categoryName =================================");
  //     switch (categoryName) {
  //       case "HolidayHomes":
  //         url = '${_endpoint}getholidayhomeimagesadmin?id=$id';
  //         break;
  //       case "CarRents":
  //         url = '${_endpoint}getcarrentimagesadmin?id=$id';
  //         break;
  //       case "Properties":
  //         url = '${_endpoint}getpropertyimagesadmin?id=$id';
  //         break;
  //       default:
  //         url = '${_endpoint}getimagesmaincategory?id=$id';
  //     }
  //     print(url);
  //     Response response = await _dio.get(
  //       url,
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           // "Content-Type": "application/x-www-form-urlencoded",
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 600;
  //           }),
  //     );
  //     print(response);
  //     return CategoryImagesResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     print("Exception occured: $error stackTrace: $stacktrace");
  //     return CategoryImagesResponse.withError(handleError(error));
  //   }
  // }

  Future<List<MainCategoryModel>> getMainCategories() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var token = prefs.getString('token');
      var userid = prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}categories';

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      log('DDD $response');
      return (response.data['data'] as List)
          .map((e) => MainCategoryModel.fromJson(e))
          .toList();
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return [];
    }
  }

  Future<CategoryReelsResponse> getMainCategoryReals(
    int id,
  ) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var token = prefs.getString('token');
      var userid = prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}reels?id=$id';

      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return CategoryReelsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CategoryReelsResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editStartEndPrice(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print('asdijd ${data.toString()}');
      Response response = await _dio.post('${_endpoint}video/editprice',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
