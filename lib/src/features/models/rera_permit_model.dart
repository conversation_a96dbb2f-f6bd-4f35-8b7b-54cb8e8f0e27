// import 'package:equatable/equatable.dart';
//
// class MediaModel extends Equatable {
//   final int? id;
//   final String? url;
//
//   const MediaModel({this.id, this.url});
//
//   factory MediaModel.fromJson(Map<String, dynamic> json) {
//     return MediaModel(
//       id: json['id'] as int?,
//       url: json['url'] as String?,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'url': url,
//     };
//   }
//
//   @override
//   List<Object?> get props => [id, url];
// }
//
// class ReraPermitModel extends Equatable {
//   final int? id;
//   final int? videoId;
//   final String? reraNumber;
//   final List<MediaModel>? media;
//
//   const ReraPermitModel({
//     this.id,
//     this.videoId,
//     this.reraNumber,
//     this.media,
//   });
//
//   factory ReraPermitModel.fromJson(Map<String, dynamic> json) {
//     return ReraPermitModel(
//       id: json['id'] as int?,
//       videoId: json['video_id'] as int?,
//       reraNumber: json['rera_number'] as String?,
//       media: json['media'] != null
//           ? List<MediaModel>.from(
//               json['media'].map((x) => MediaModel.fromJson(x)))
//           : [],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'video_id': videoId,
//       'rera_number': reraNumber,
//       'media': media?.map((x) => x.toJson()).toList(),
//     };
//   }
//
//   ReraPermitModel copyWith({
//     int? id,
//     int? videoId,
//     String? reraNumber,
//     List<MediaModel>? media,
//   }) {
//     return ReraPermitModel(
//       id: id ?? this.id,
//       videoId: videoId ?? this.videoId,
//       reraNumber: reraNumber ?? this.reraNumber,
//       media: media ?? this.media,
//     );
//   }
//
//   @override
//   List<Object?> get props => [id, videoId, reraNumber, media];
// }
