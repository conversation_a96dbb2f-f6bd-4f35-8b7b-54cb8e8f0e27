class MediaModel {
  int? id;
  String? url;

  MediaModel({this.id, this.url});

  MediaModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    url = json['url'];
  }
}

class FloorPlanModel {
  int? id;
  int? videoId;
  String? name;
  String? nameAr;
  String? nameEn;
  List<MediaModel>? media;

  FloorPlanModel({
    this.id,
    this.videoId,
    this.name,
    this.nameAr,
    this.nameEn,
    this.media,
  });

  FloorPlanModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }
}

class ReraPermitModel {
  int? id;
  int? videoId;
  String? reraNumber;
  List<MediaModel>? media;

  ReraPermitModel({
    this.id,
    this.videoId,
    this.reraNumber,
    this.media,
  });

  ReraPermitModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    reraNumber = json['rera_number'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }
}
