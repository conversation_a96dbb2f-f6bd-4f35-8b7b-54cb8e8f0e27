import 'package:page/src/features/controllers/language_controller.dart';

class ProjectPlanModel {
  String? bedroomsEn;
  String? bedroomsAr;
  String? priceFrom;
  String? priceTo;
  String? spaceSizeEn;
  String? spaceSizeAr;

  ProjectPlanModel({
    this.bedroomsEn,
    this.bedroomsAr,
    this.priceFrom,
    this.priceTo,
    this.spaceSizeEn,
    this.spaceSizeAr,
  });

  ProjectPlanModel.fromJson(Map<String, dynamic> json) {
    bedroomsEn = json['bedrooms_en'] ?? '';
    bedroomsAr = json['bedrooms_ar'] ?? '';
    priceFrom = json['price_from']?.toString() ?? '';
    priceTo = json['price_to']?.toString() ?? '';
    spaceSizeEn = json['space_size_en'] ?? '';
    spaceSizeAr = json['space_size_ar'] ?? '';
  }

  String get bedrooms {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? bedroomsEn ?? bedroomsAr ?? ''
        : bedroomsAr ?? bedroomsEn ?? '';
  }

  String get spaceSize {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? spaceSizeEn ?? ''
        : spaceSizeAr ?? '';
  }
}

class MediaModel {
  int? id;
  String? url;

  MediaModel({this.id, this.url});

  MediaModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    url = json['url'];
  }
}

class FloorPlanModel {
  int? id;
  int? videoId;
  String? name;
  String? nameAr;
  String? nameEn;
  List<MediaModel>? media;

  FloorPlanModel({
    this.id,
    this.videoId,
    this.name,
    this.nameAr,
    this.nameEn,
    this.media,
  });

  FloorPlanModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }

  String get displayName {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? nameEn ?? name ?? ''
        : nameAr ?? name ?? '';
  }
}

class ReraPermitModel {
  int? id;
  int? videoId;
  String? reraNumber;
  List<MediaModel>? media;

  ReraPermitModel({
    this.id,
    this.videoId,
    this.reraNumber,
    this.media,
  });

  ReraPermitModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    reraNumber = json['rera_number'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }
}

class PropertyStatusModel {
  int? id;
  String? name;
  String? nameAr;
  String? nameEn;

  PropertyStatusModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
  });

  PropertyStatusModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
  }

  String get displayName {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? nameEn ?? name ?? ''
        : nameAr ?? name ?? '';
  }
}

class DeveloperModel {
  int? id;
  String? name;
  String? nameAr;
  String? nameEn;
  String? description;
  String? descriptionAr;
  String? descriptionEn;
  String? developerLogo;

  DeveloperModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
    this.description,
    this.descriptionAr,
    this.descriptionEn,
    this.developerLogo,
  });

  DeveloperModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
    description = json['description'];
    descriptionAr = json['description_ar'];
    descriptionEn = json['description_en'];
    developerLogo = json['developer_logo'];
  }

  String get displayName {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? nameEn ?? name ?? ''
        : nameAr ?? name ?? '';
  }

  String get displayDescription {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? descriptionEn ?? description ?? ''
        : descriptionAr ?? description ?? '';
  }
}
