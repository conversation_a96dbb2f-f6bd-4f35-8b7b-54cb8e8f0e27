import 'dart:developer';

import 'package:admin_dubai/src/features/bloc/auth_blok.dart';
import 'package:admin_dubai/src/features/models/agent_list_model.dart';
import 'package:admin_dubai/src/features/models/main_model.dart';
import 'package:admin_dubai/src/features/models/project_models.dart';

import 'main_category_model.dart';

class CategoryModel {
  int? id;
  int? agentId;
  String? name;
  String? nameAr;
  String? nameEn;
  String? description;
  String? descriptionAr;
  String? descriptionEn;
  num? startPrice;
  num? endPrice;
  num? price;
  String? image;
  String? video;
  String? videoAr;
  String? rooms;
  int? size;
  int? typeId;
  String? type;
  String? roomnumber;

  // var label;
  String? website;
  String? websiteEn;
  String? websiteAr;
  String? instagram;
  String? whatsapp;
  String? locationName;
  MainCategoryModel? category;
  String? photo;
  String? label;
  MainModel? location;
  int? numberofstarts;
  double? lat;
  double? lng;
  int? isFav;
  AgentListModel? agent;

  // int? parentId
  String? googleReviewLink;
  String? googleReviewName;
  String? phone;
  String? createdAt;
  String? updatedAt;
  double? rating;
  num? privatePrice;
  String? brand;
  int? brandId;
  String? year;
  int? yearId;
  String? feature;
  String? currency;

  int? startSize;
  int? endSize;

  int? featuredHome;
  int? featuredCategory;

  List<int>? features;

  int? rmsPropertyId;
  int? rmsCategoryId;
  int? rmsAreaId;

  CategoryModel({
    this.id,
    this.name,
    this.description,
    this.startPrice,
    this.endPrice,
    this.price,
    this.image,
    this.video,
    this.videoAr,
    this.rooms,
    this.size,
    this.type,
    this.website,
    this.websiteEn,
    this.websiteAr,
    this.instagram,
    this.locationName,
    this.whatsapp,
    this.category,
    this.photo,
    this.label,
    this.location,
    this.numberofstarts,
    this.lat,
    this.lng,
    this.isFav,
    this.agent,
    this.features,
    this.googleReviewLink,
    this.googleReviewName,
    this.phone,
    this.createdAt,
    this.updatedAt,
    this.rating,
    this.privatePrice,
    this.brand,
    this.year,
    this.feature,
    this.roomnumber,
    this.startSize,
    this.endSize,
    this.currency,
    this.featuredHome,
    this.featuredCategory,
    this.brandId,
    this.typeId,
    this.yearId,
    this.agentId,
    this.rmsPropertyId,
    this.rmsCategoryId,
    this.rmsAreaId,
  });

  CategoryModel.fromJson(Map<String, dynamic> json) {
    final isEng = AuthBloc.isEnglish;

    id = json['id'];
    roomnumber = json['rooms'];
    name = json['name'];
    description = json['description'];
    rooms = json['rooms'];
    size = json['size'];

    year = json['year_car'] != null ? json['year_car']['year'] : '';

    price = json['price'];

    startSize = json['start_size'];
    endSize = json['end_size'];

    currency = json['currency'];

    privatePrice = num.tryParse(json['private_driver_price'].toString());

    agent =
        json['agent'] != null ? AgentListModel.fromJson(json['agent']) : null;

    isFav = json['is_favorite'] == true ? 1 : 0;

    whatsapp = json['whatsapp'];

    features = json['features'] != null && json['features'].length > 0
        ? List<int>.from(json['features'].map((x) => x['id']))
        : [];

    image = json['images'] != null && json['images'].isNotEmpty
        ? json['images'][0]['url']
        : '';
    startPrice = json['startprice'] == 0 ? json['price'] : json['startprice'];
    endPrice = json['endprice'];

    brand = json['brand_car'] != null
        ? isEng
            ? json['brand_car']['name']['en']
            : json['brand_car']['name']['ar']
        : '';

    brandId = json['brand_car'] != null ? json['brand_car']['id'] : null;

    typeId = json['type'] != null ? json['type']['id'] : null;

    type = json['type'] == null
        ? ''
        : isEng
            ? json['type']['name']['en']
            : json['type']['name']['ar'];
    feature = json['features'] != null && json['features'].length > 0
        ? isEng
            ? json['features'][0]['name']['en']
            : json['features'][0]['name']['ar']
        : '';

    locationName = json['location'] != null ? json['location']['name'] : '';

    video = json['video'];
    videoAr = json['video_ar'];
    year = json['year_car'] != null ? json['year_car']['year'] : '';
    website = json['website'];
    websiteEn = json['website_en'];
    websiteAr = json['website_ar'];
    instagram = json['instagram'];
    agentId = json['agent'] != null ? json['agent']['id'] : null;
    category = json['category'] != null
        ? MainCategoryModel.fromJson(json['category'])
        : null;

    featuredHome = json['featuredHome'];

    featuredCategory = json['featuredCategory'];

    //////// ! ====================

    label = category?.name;

    name = json['name'];

    nameAr = json['name_ar'];

    nameEn = json['name_en'];

    description = json['description'];

    descriptionAr = json['description_ar'];

    descriptionEn = json['description_en'];

    year = json['year_car'] != null ? json['year_car']['year'] : '';

    yearId = json['year_car'] != null ? json['year_car']['id'] : null;

    photo = json['images'] != null && json['images'].isNotEmpty
        ? json['images'][0]['url']
        : '';

    location =
        json['location'] == null ? null : MainModel.fromJson(json['location']);

    lat = double.tryParse(json['latitude'].toString());
    lng = double.tryParse(json['longitude'].toString());

    googleReviewLink = json['review_link'];
    googleReviewName = json['review_name'];
    phone = json['phone'];
    website = json['website'];
    instagram = json['instagram'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    rating = double.tryParse(json['stars']?.toString() ?? '0');

    rmsPropertyId = json['rms_property_id'];
    rmsCategoryId = json['rms_category_id'];
    rmsAreaId = json['rms_area_id'];
  }
}

class CategoryDetails {
  int? id;
  String? name;
  int? startprice;
  int? endprice;
  int? numberofstarts;
  String? locationName;
  String? description;
  double? latitude;
  double? longitude;
  String? video;

  CategoryDetails.fromJson(Map<String?, dynamic> json) {
    log('asdasd ${json}');
    id = json['id'];
    name = json['name'];
    startprice = json['startprice'];
    endprice = json['endprice'];
    numberofstarts = json['numberofstarts'];
    locationName = json['location_name'];
    description = json['description'];
    latitude = double.tryParse(json['latitude'].toString());
    longitude = double.tryParse(json['longitude'].toString());
    video = json['video'];
  }
}
