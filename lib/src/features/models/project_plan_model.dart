import 'package:equatable/equatable.dart';

class ProjectPlanModel extends Equatable {
  final String? bedroomsAr;
  final String? bedroomsEn;
  final String? priceFrom;
  final String? priceTo;
  final String? spaceSizeAr;
  final String? spaceSizeEn;
  final String id; // Unique identifier for each item

  const ProjectPlanModel({
    this.bedroomsAr,
    this.bedroomsEn,
    this.priceFrom,
    this.priceTo,
    this.spaceSizeAr,
    this.spaceSizeEn,
    String? id,
  }) : id = id ?? '';

  factory ProjectPlanModel.fromJson(Map<String, dynamic> json) {
    return ProjectPlanModel(
      bedroomsAr: json['bedrooms_ar'] as String?,
      bedroomsEn: json['bedrooms_en'] as String?,
      priceFrom: json['price_from'] as String?,
      priceTo: json['price_to'] as String?,
      spaceSizeAr: json['space_size_ar'] as String?,
      spaceSizeEn: json['space_size_en'] as String?,
      id: json['id']?.toString() ??
          DateTime.now().millisecondsSinceEpoch.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['bedrooms'] = {
      'ar': bedroomsAr,
      'en': bedroomsEn,
    };
    data['price_from'] = priceFrom;
    data['price_to'] = priceTo;
    data['space_size'] = {
      'ar': spaceSizeAr,
      'en': spaceSizeEn,
    };
    // Note: ID is not included in JSON as it's only for UI purposes
    return data;
  }

  ProjectPlanModel copyWith({
    String? bedroomsAr,
    String? bedroomsEn,
    String? priceFrom,
    String? priceTo,
    String? spaceSizeAr,
    String? spaceSizeEn,
    String? id,
  }) {
    return ProjectPlanModel(
      bedroomsAr: bedroomsAr ?? this.bedroomsAr,
      bedroomsEn: bedroomsEn ?? this.bedroomsEn,
      priceFrom: priceFrom ?? this.priceFrom,
      priceTo: priceTo ?? this.priceTo,
      spaceSizeAr: spaceSizeAr ?? this.spaceSizeAr,
      spaceSizeEn: spaceSizeEn ?? this.spaceSizeEn,
      id: id ?? this.id,
    );
  }

  @override
  List<Object?> get props => [
        bedroomsAr,
        bedroomsEn,
        priceFrom,
        priceTo,
        spaceSizeAr,
        spaceSizeEn,
        id,
      ];
}
