import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_radius.dart';
import '../../../../core/resources/theme/theme.dart';
import '../../../orders/models/order/order_model.dart';

class InvoiceTitleSection extends StatelessWidget {
  final OrderModel order;

  const InvoiceTitleSection({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final totalProductsQuantity = order.products?.fold(
        0, (previousValue, element) => previousValue + element.quantity!);

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      width: double.infinity,
      decoration: BoxDecoration(
          color: context.appTheme.cardColor,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: context.isDark
              ? ConstantsWidgets.darkBoxShadowFromBottom
              : ConstantsWidgets.boxShadowFromBottom),
      child: Row(
        children: [
          //! Total Price & Created At
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //! Total Products
              Row(
                children: [
                  Text(
                    '${context.tr.totalProducts}:',
                    style: context.labelLarge,
                  ),
                  context.smallGap,
                  Text(
                    totalProductsQuantity.toString(),
                    style: context.subTitle
                        .copyWith(color: ColorManager.primaryColor),
                  ),
                ],
              ),

              //! Total Price
              Row(
                children: [
                  Text(
                    context.tr.totalPrice,
                    style: context.labelLarge,
                  ),
                  context.smallGap,
                  Text(
                    order.total.toCurrency(context),
                    style: context.subTitle
                        .copyWith(color: ColorManager.primaryColor),
                  ),
                ],
              ),

              Text(
                order.createdAt!.formatDateToStringWithTime,
                style: context.greyLabelLarge,
              ),
            ],
          ),
          const Spacer(),

          CircleAvatar(
            backgroundColor: ColorManager.primaryColor,
            radius: 20,
            child: IconButton(
              onPressed: () {
                context
                    .read<OrderVM>()
                    .generateStoreOrderInvoice(context, order: order);
              },
              icon: const Icon(
                Icons.print,
                color: ColorManager.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
