// import 'dart:math';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
// import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
// import 'package:idea2app_vendor_app/src/screens/reports/model/report_model.dart';
//
// class TotalUsersChartsWidget extends StatefulWidget {
//   final List<DailyEarningsModel> dailyEarnings;
//
//   const TotalUsersChartsWidget({super.key, required this.dailyEarnings});
//
//   @override
//   State<TotalUsersChartsWidget> createState() => _TotalUsersChartsWidgetState();
// }

//
// class _TotalUsersChartsWidgetState extends State<TotalUsersChartsWidget> {
//   List<Color> gradientColors = [
//     ColorManager.primaryColor,
//     Colors.transparent,
//   ];
//
//   @override
//   Widget build(BuildContext context) {
//     if (widget.dailyEarnings.isEmpty || widget.dailyEarnings.length == 1) {
//       return Container(
//           width: double.infinity,
//           height: 250.h,
//           child: Center(
//               child: Text(context.tr.noDataAvailable, style: context.title)));
//     }
//
//     return Container(
//       padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//       child: AspectRatio(
//         aspectRatio: 1.70,
//         child: LineChart(
//           curve: Curves.ease,
//           duration: const Duration(milliseconds: 250),
//           mainData(),
//         ),
//       ),
//     );
//   }
//
//   Widget bottomTitleWidgets(double value, TitleMeta meta) {
//     final monthNames = [
//       'Jan',
//       'Feb',
//       'Mar',
//       'Apr',
//       'May',
//       'Jun',
//       'Jul',
//       'Aug',
//       'Sep',
//       'Oct',
//       'Nov',
//       'Dec'
//     ];
//
//     final monthNumbers = widget.dailyEarnings.map((e) => e.date.month).toList();
//
//     const style = TextStyle(
//       color: ColorManager.black,
//       fontSize: 12,
//     );
//
//     Widget text;
//
//     if (value.toInt() >= 1 && value.toInt() <= monthNumbers.length) {
//       final monthIndex = monthNumbers[value.toInt() - 1] - 1;
//       text = Text(monthNames[monthIndex], style: style);
//       // Log.f(
//       //     "monthNames: ${monthNames[monthIndex]} monthNumbers: ${monthNumbers[monthIndex]} ");
//     } else {
//       return Container();
//     }
//
//     return SideTitleWidget(
//       axisSide: meta.axisSide,
//       child: text,
//     );
//   }
//
//   Widget leftTitleWidgets(double value, TitleMeta meta) {
//     const style = TextStyle(
//       color: ColorManager.black,
//       fontSize: 12,
//     );
//     String text;
//
//     final maxValue =
//         max(0, widget.dailyEarnings.map((e) => e.earnings).reduce(max));
//     final isMoreThanOneM = maxValue >= 1000000;
//     final total = isMoreThanOneM ? maxValue : 1000000;
//
//     final totalWithM = (total / 1000000)
//         .toStringAsFixed(0); // Format to 1 decimal place if needed
//
//     switch (value.toInt()) {
//       case 0:
//         text = '0';
//       case 2:
//         text = '200k';
//         break;
//       case 4:
//         text = '400k';
//         break;
//       case 6:
//         text = '600k';
//       case 8:
//         text = '800k';
//       case 10:
//         text = '1M';
//         // isMoreThanOneM
//         //     ? '$totalWithM M'
//         //     : isMoreThanOneM
//         //         ? total.toInt().toString()
//         //         : '1M';
//         break;
//       default:
//         return Container();
//     }
//
//     return Text(text, style: style, textAlign: TextAlign.left);
//   }
//
//   LineChartData mainData() {
//     return LineChartData(
//       lineTouchData: const LineTouchData(
//         enabled: false,
//       ),
//       borderData: FlBorderData(
//         show: true,
//         border: Border.all(color: ColorManager.grey, width: 1),
//       ),
//       titlesData: FlTitlesData(
//         show: true,
//         rightTitles: const AxisTitles(
//           sideTitles: SideTitles(showTitles: false),
//         ),
//         topTitles: const AxisTitles(
//           sideTitles: SideTitles(showTitles: false),
//         ),
//         bottomTitles: AxisTitles(
//           sideTitles: SideTitles(
//             showTitles: true,
//             reservedSize: 30,
//             interval: 1,
//             getTitlesWidget: bottomTitleWidgets,
//           ),
//         ),
//         leftTitles: AxisTitles(
//           sideTitles: SideTitles(
//             showTitles: true,
//             interval: 1,
//             getTitlesWidget: (value, meta) => leftTitleWidgets(value, meta),
//             reservedSize: 30,
//           ),
//         ),
//       ),
//       minX: 1,
//       maxX: widget.dailyEarnings.length.toDouble(),
//       minY: 0,
//       maxY: 10,
//       lineBarsData: [
//         LineChartBarData(
//           spots: List.generate(widget.dailyEarnings.length, (index) {
//             final totalList = widget.dailyEarnings.map((e) {
//               final totalPercentage = e.earnings / 1000;
//
//               // final isMoreThanOneM = e.earnings >= 1000000;
//               // final isEqualOneM = e.earnings == 1000000;
//
//               // final total = isEqualOneM
//               //     ? 8.5
//               //     : isMoreThanOneM
//               //         ? 9.8
//               //         : totalPercentage < 1
//               //             ? .3
//               //             : totalPercentage;
//
//               return totalPercentage;
//             }).toList();
//
//             return FlSpot((index + 1).toDouble(), totalList[index].toDouble());
//           }),
//           isCurved: true,
//           gradient: LinearGradient(
//             begin: Alignment.topCenter,
//             end: Alignment.bottomCenter,
//             colors: [
//               ColorManager.primaryColor,
//               ColorManager.primaryColor.withOpacity(0.3)
//             ],
//           ),
//           barWidth: 5,
//           isStrokeCapRound: true,
//           dotData: const FlDotData(
//             show: false,
//           ),
//           belowBarData: BarAreaData(
//             show: true,
//             gradient: LinearGradient(
//               begin: Alignment.topCenter,
//               end: Alignment.bottomCenter,
//               colors: gradientColors
//                   .map((color) => color.withOpacity(0.3))
//                   .toList(),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
