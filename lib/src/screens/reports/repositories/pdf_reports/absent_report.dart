import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import '../report_repository_helper.dart';

Future<pw.Document> generateAbsentReportPDF(BuildContext appContext,
    {required UserModel employee,
    required List<TaskModel> tasks,
    required Map<DateTime, List<UserModel?>> absentEmployees,
    required DateTime fromDate,
    required DateTime toDate}) async {
  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  final isEnglish = appContext.isAppEnglish;

  englishTable() {
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            baseText(
              appContext.tr.employee,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.date,
              arabicFont: arabicFont,
              isBold: true,
            ),
          ],
        ),
        // if (absentEmployees.isNotEmpty)
        ...absentEmployees.entries
            .where((element) => element.value.isNotEmpty)
            .map(
              (report) => pw.TableRow(
                children: [
                  baseText(
                    report.value.map((e) => e?.name ?? '').join(', '),
                    arabicFont: arabicFont,
                  ),
                  baseText(
                    report.key.formatDateToString,
                    arabicFont: arabicFont,
                  ),
                ],
              ),
            )
        // else
        // ...tasks.map(
        //   (report) => pw.TableRow(
        //     children: [
        //       baseText(
        //         report.project,
        //         arabicFont: arabicFont,
        //       ),
        //       baseText(
        //         report.date.formatDateToString,
        //         arabicFont: arabicFont,
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  //? reverse (Description, End Time, Start Time, Project)
  arabicTable() {
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            baseText(
              appContext.tr.description,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.endDate,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.startDate,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.project,
              arabicFont: arabicFont,
              isBold: true,
            ),
          ],
        ),
        ...tasks.map(
          (report) => pw.TableRow(
            children: [
              baseText(
                report.description,
                arabicFont: arabicFont,
              ),
              baseText(
                report.closedAt?.toDate().formatDateToStringWithTime ?? '-',
                arabicFont: arabicFont,
                isLtr: true,
              ),
              baseText(
                report.createdAt?.toDate().formatDateToStringWithTime ?? '-',
                arabicFont: arabicFont,
                isLtr: true,
              ),
              baseText(
                report.project,
                arabicFont: arabicFont,
              ),
            ],
          ),
        ),
      ],
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
            level: 0,
            child: pw.Text(
              '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          if (isEnglish) englishTable() else arabicTable()
        ];
      },
    ),
  );

  return pdf;
}
