import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/attendance/models/attendance_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/report_repository_helper.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import 'attendance_report_helper.dart';

Future<pw.Document> generateSingleEmployeeAttendanceReportPDF(
    BuildContext appContext,
    {required UserModel employee,
    required List<AttendanceModel> attendances,
    required List<VacationModel> allVacations,
    required DateTime fromDate,
    required DateTime toDate}) async {
  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  final isEnglish = appContext.isAppEnglish;

  List<pw.TableRow> generateAttendanceRows(List<AttendanceModel> attendances,
      {required pw.Font arabicFont, bool isArabic = false}) {
    var rows = <pw.TableRow>[];

    for (var date = fromDate;
        date.isBefore(toDate) || date.isSameDay(toDate);
        date = date.add(Duration(days: 1))) {
      var attendance = attendances.firstWhereOrNull(
        (att) => att.date?.isSameDay(date) == true,
      );

      var vacationsForEmployee = allVacations
          .where((vacation) =>
              vacation.uid == employee.uid &&
              vacation.isApproved == true &&
              vacation.fromDate != null &&
              vacation.toDate != null &&
              (vacation.fromDate!.isBefore(date) ||
                  vacation.fromDate!.isAtSameMomentAs(date)) &&
              (vacation.toDate!.isAfter(date) ||
                  vacation.toDate!.isAtSameMomentAs(date)))
          .toList();

      final isOnVacation = vacationsForEmployee.isNotEmpty;

      final checkIns = isOnVacation
          ? (isArabic ? 'إجازة' : 'Vacation')
          : attendance?.checkedInAt.join('\n') ??
              (isArabic ? 'غائب' : 'Absent');
      final partCheckOuts = isOnVacation
          ? '-'
          : attendance?.partCheckoutReason.entries
                  .map((e) => e.key)
                  .join('\n') ??
              '-';
      final partCheckOutsReasons = isOnVacation
          ? '-'
          : attendance?.partCheckoutReason.entries
                  .map((e) => e.value)
                  .join('\n') ??
              '-';
      final checkOut = isOnVacation ? '-' : attendance?.checkedOutAt ?? '-';
      final totalAttendanceTime = isOnVacation
          ? '-'
          : attendance != null
              ? formatDuration(
                  calculateTotalAttendanceTime(attendance), isArabic)
              : '-';
      final totalPartialTimes = isOnVacation
          ? '-'
          : attendance != null
              ? formatDuration(calculateTotalPartialTimes(attendance), isArabic)
              : '-';

      final textColor = PdfColors.black;

      final isFriday = date.weekday == DateTime.friday;

      final decorationColor = isFriday
          ? PdfColors.yellow50
          : isOnVacation
              ? PdfColors.amber50
              : attendance == null
                  ? PdfColors.red50
                  : null;

      if (isArabic) {
        rows.add(pw.TableRow(
          decoration: pw.BoxDecoration(
            color: decorationColor,
          ),
          children: [
            baseText(totalPartialTimes,
                arabicFont: arabicFont, color: textColor),
            baseText(totalAttendanceTime,
                arabicFont: arabicFont, color: textColor),
            baseText(checkOut, arabicFont: arabicFont, color: textColor),
            baseText(partCheckOutsReasons,
                arabicFont: arabicFont, color: textColor),
            baseText(partCheckOuts, arabicFont: arabicFont, color: textColor),
            baseText(checkIns, arabicFont: arabicFont, color: textColor),
            baseText(
                '${date.formatDateToString}\n${isEnglish ? date.dayNameEn : date.dayNameAr}',
                arabicFont: arabicFont,
                color: textColor),
          ],
        ));
      } else {
        rows.add(pw.TableRow(
          decoration: pw.BoxDecoration(
            color: decorationColor,
          ),
          children: [
            baseText(
              date.formatDateToString,
              arabicFont: arabicFont,
              color: textColor,
            ),
            baseText(checkIns, arabicFont: arabicFont, color: textColor),
            baseText(partCheckOuts, arabicFont: arabicFont, color: textColor),
            baseText(partCheckOutsReasons,
                arabicFont: arabicFont, color: textColor),
            baseText(checkOut, arabicFont: arabicFont, color: textColor),
            baseText(totalAttendanceTime,
                arabicFont: arabicFont, color: textColor),
            baseText(totalPartialTimes,
                arabicFont: arabicFont, color: textColor),
          ],
        ));
      }
    }

    return rows;
  }

  pw.Widget englishTable(List<AttendanceModel> attendances) {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText('Date', arabicFont: arabicFont, isBold: true),
          baseText('Check-Ins', arabicFont: arabicFont, isBold: true),
          baseText('Partial Times', arabicFont: arabicFont, isBold: true),
          baseText('Partial Reasons', arabicFont: arabicFont, isBold: true),
          baseText('Check-Out', arabicFont: arabicFont, isBold: true),
          baseText('Total Attendance Time',
              arabicFont: arabicFont, isBold: true),
          baseText('Total Partial Times', arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> attendanceRows =
        generateAttendanceRows(attendances, arabicFont: arabicFont);
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(1),
        6: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
      children: headerRow + attendanceRows,
    );
  }

  pw.Widget arabicTable(List<AttendanceModel> attendances) {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText('إجمالي أوقات الخروج الجزئي',
              arabicFont: arabicFont, isBold: true),
          baseText('إجمالي وقت الحضور', arabicFont: arabicFont, isBold: true),
          baseText('وقت الخروج', arabicFont: arabicFont, isBold: true),
          baseText('سبب الخروج الجزئي', arabicFont: arabicFont, isBold: true),
          baseText('وقت الخروج الجزئي', arabicFont: arabicFont, isBold: true),
          baseText('وقت الدخول', arabicFont: arabicFont, isBold: true),
          baseText('التاريخ', arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> attendanceRows = generateAttendanceRows(attendances,
        arabicFont: arabicFont, isArabic: true);
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(1),
        6: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
      children: headerRow + attendanceRows,
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
            level: 0,
            child: pw.Text(
              '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          isEnglish ? englishTable(attendances) : arabicTable(attendances),
        ];
      },
    ),
  );

  return pdf;
}
//
// Future<pw.Document> generateSingleEmployeeAttendanceReportPDF(
//     BuildContext appContext,
//     {required UserModel employee,
//     required List<AttendanceModel> attendances,
//     required List<VacationModel> allVacations,
//     required DateTime fromDate,
//     required DateTime toDate}) async {
//   final arabicFont = Font.ttf(
//     await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
//   );
//
//   final pdf = pw.Document();
//
//   final isEnglish = appContext.isAppEnglish;
//
//   String formatDuration(Duration duration, bool isArabic) {
//     if (duration.inMinutes < 60) {
//       return isArabic
//           ? '${duration.inMinutes} دقيقة'
//           : '${duration.inMinutes} min';
//     } else {
//       final hours = duration.inHours;
//       final minutes = duration.inMinutes % 60;
//       if (minutes == 0) {
//         return isArabic ? '$hours ساعة' : '$hours hour';
//       } else {
//         return isArabic
//             ? '$hours ساعة و $minutes دقيقة'
//             : '$hours hour and $minutes min';
//       }
//     }
//   }
//
//   List<pw.TableRow> generateAttendanceRows(List<AttendanceModel> attendances,
//       {required pw.Font arabicFont, bool isArabic = false}) {
//     var rows = <pw.TableRow>[];
//
//     for (var attendance in attendances) {
//       final checkIns = attendance.checkedInAt.join('\n');
//       final partCheckOuts =
//           attendance.partCheckoutReason.entries.map((e) => e.key).join('\n');
//       final partCheckOutsReasons =
//           attendance.partCheckoutReason.entries.map((e) => e.value).join('\n');
//       final checkOut = attendance.checkedOutAt ?? '-';
//       final totalAttendanceTime =
//           formatDuration(calculateTotalAttendanceTime(attendance), isArabic);
//       final totalPartialTimes =
//           formatDuration(calculateTotalPartialTimes(attendance), isArabic);
//
//       if (isArabic) {
//         rows.add(pw.TableRow(
//           children: [
//             baseText(totalPartialTimes, arabicFont: arabicFont),
//             baseText(totalAttendanceTime, arabicFont: arabicFont),
//             baseText(checkOut, arabicFont: arabicFont),
//             baseText(partCheckOutsReasons, arabicFont: arabicFont),
//             baseText(partCheckOuts, arabicFont: arabicFont),
//             baseText(checkIns, arabicFont: arabicFont),
//             baseText(attendance.date?.formatDateToString ?? '-',
//                 arabicFont: arabicFont),
//           ],
//         ));
//       } else {
//         rows.add(pw.TableRow(
//           children: [
//             baseText(attendance.date?.formatDateToString ?? '-',
//                 arabicFont: arabicFont),
//             baseText(checkIns, arabicFont: arabicFont),
//             baseText(partCheckOuts, arabicFont: arabicFont),
//             baseText(partCheckOutsReasons, arabicFont: arabicFont),
//             baseText(checkOut, arabicFont: arabicFont),
//             baseText(totalAttendanceTime, arabicFont: arabicFont),
//             baseText(totalPartialTimes, arabicFont: arabicFont),
//           ],
//         ));
//       }
//     }
//
//     return rows;
//   }
//
//   pw.Widget englishTable(List<AttendanceModel> attendances) {
//     List<pw.TableRow> headerRow = [
//       pw.TableRow(
//         children: [
//           baseText('Date', arabicFont: arabicFont, isBold: true),
//           baseText('Check-Ins', arabicFont: arabicFont, isBold: true),
//           baseText('Partial Times', arabicFont: arabicFont, isBold: true),
//           baseText('Partial Reasons', arabicFont: arabicFont, isBold: true),
//           baseText('Check-Out', arabicFont: arabicFont, isBold: true),
//           baseText('Total Attendance Time',
//               arabicFont: arabicFont, isBold: true),
//           baseText('Total Partial Times', arabicFont: arabicFont, isBold: true),
//         ],
//       ),
//     ];
//     List<pw.TableRow> attendanceRows =
//         generateAttendanceRows(attendances, arabicFont: arabicFont);
//     return pw.Table(
//       columnWidths: {
//         0: const pw.FlexColumnWidth(1),
//         1: const pw.FlexColumnWidth(1),
//         2: const pw.FlexColumnWidth(1),
//         3: const pw.FlexColumnWidth(1),
//         4: const pw.FlexColumnWidth(1),
//         5: const pw.FlexColumnWidth(1),
//         6: const pw.FlexColumnWidth(1),
//       },
//       border: pw.TableBorder.all(),
//       defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
//       children: headerRow + attendanceRows,
//     );
//   }
//
//   pw.Widget arabicTable(List<AttendanceModel> attendances) {
//     List<pw.TableRow> headerRow = [
//       pw.TableRow(
//         children: [
//           baseText('إجمالي أوقات الخروج الجزئي',
//               arabicFont: arabicFont, isBold: true),
//           baseText('إجمالي وقت الحضور', arabicFont: arabicFont, isBold: true),
//           baseText('وقت الخروج', arabicFont: arabicFont, isBold: true),
//           baseText('سبب الخروج الجزئي', arabicFont: arabicFont, isBold: true),
//           baseText('وقت الخروج الجزئي', arabicFont: arabicFont, isBold: true),
//           baseText('وقت الدخول', arabicFont: arabicFont, isBold: true),
//           baseText('التاريخ', arabicFont: arabicFont, isBold: true),
//         ],
//       ),
//     ];
//     List<pw.TableRow> attendanceRows = generateAttendanceRows(attendances,
//         arabicFont: arabicFont, isArabic: true);
//     return pw.Table(
//       columnWidths: {
//         0: const pw.FlexColumnWidth(1),
//         1: const pw.FlexColumnWidth(1),
//         2: const pw.FlexColumnWidth(1),
//         3: const pw.FlexColumnWidth(1),
//         4: const pw.FlexColumnWidth(1),
//         5: const pw.FlexColumnWidth(1),
//         6: const pw.FlexColumnWidth(1),
//       },
//       border: pw.TableBorder.all(),
//       defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
//       children: headerRow + attendanceRows,
//     );
//   }
//
//   pdf.addPage(
//     pw.MultiPage(
//       pageFormat: PdfPageFormat.a4,
//       build: (pw.Context context) {
//         return <pw.Widget>[
//           pw.Header(
//             level: 0,
//             child: pw.Text(
//               '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
//               textScaleFactor: 2,
//               textAlign: pw.TextAlign.center,
//               textDirection: pw.TextDirection.rtl,
//               style: pw.TextStyle(
//                 font: arabicFont,
//                 fontWeight: pw.FontWeight.bold,
//               ),
//             ),
//           ),
//           isEnglish ? englishTable(attendances) : arabicTable(attendances),
//         ];
//       },
//     ),
//   );
//
//   return pdf;
// }
