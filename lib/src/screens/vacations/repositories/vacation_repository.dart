import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/firestore_strings.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firestore_service.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:xr_helper/xr_helper.dart';

final vacationRepoProvider = Provider<VacationRepository>((ref) {
  return VacationRepository();
});

class VacationRepository with BaseRepository {
  //? Add Vacation To Firestore
  Future<void> addVacation({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.createData(
          collection: FirebaseStrings.vacations,
          data: vacation.toJson(),
        );
      },
    );
  }

  // //? Update Vacation To Firestore
  // Future<void> updateVacation({
  //   required VacationModel vacation,
  // }) async {
  //   await baseFunction(
  //     () async {
  //       await FirestoreService.updateData(
  //         collection: FirebaseStrings.vacations,
  //         id: vacation.id!,
  //         data: vacation.toJson(),
  //       );
  //     },
  //   );
  // }

  //? Get Vacation Data From Same Login Id From Firestore
  Future<List<VacationModel>> getVacations(
      {required DateTime date,
      required DateTime toDate,
      String? userId}) async {
    try {
      List<VacationModel> vacations = [];

      //? All Employees
      if (userId == null) {
        vacations = await FirestoreService.getDataBy2FieldsByDateRange(
            collection: FirebaseStrings.vacations,
            field1: FirebaseStrings.fromDate,
            value1: date.formatDateToString,
            field2: FirebaseStrings.toDate,
            value2: toDate.formatDateToString,
            builder: (id, data) {
              return VacationModel.fromJson(id, data: data);
            });
      } else {
        vacations = await FirestoreService.getDataBy3Fields(
            collection: FirebaseStrings.vacations,
            field1: FirebaseStrings.uid,
            value1: userId,
            field2: FirebaseStrings.fromDate,
            value2: date.formatDateToString,
            field3: FirebaseStrings.toDate,
            value3: toDate.formatDateToString,
            builder: (id, data) {
              return VacationModel.fromJson(id, data: data);
            });
      }

      //? sort vacations by date
      vacations.sort((a, b) => a.createdAt!.compareTo(b.createdAt!));
      return vacations;
    } catch (e) {
      rethrow;
    }
  }

  Stream<List<VacationModel>> getVacationsStream({
    String? userId,
  }) {
    return FirestoreService.getStreamData(
      collection: FirebaseStrings.vacations,
      withDateSort: true,
      builder: (id, data) {
        return VacationModel.fromJson(id, data: data);
      },
    );
  }

  //? Approve Vacations
  Future<void> approveVacations({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.vacations,
          id: vacation.id!,
          data: vacation.toJsonApprove(),
        );
      },
    );
  }

  //? Reject Vacations
  Future<void> rejectVacations({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.vacations,
          id: vacation.id!,
          data: vacation.toJsonReject(),
        );
      },
    );
  }

  //? Delete Vacation
  Future<void> deleteVacation({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      () async {
        FirestoreService.deleteData(
          collection: FirebaseStrings.vacations,
          id: vacation.id!,
        );
      },
    );
  }
}
