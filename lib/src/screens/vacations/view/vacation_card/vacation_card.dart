import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../widgets/admin_vacation_actions.dart';

class VacationCard extends StatelessWidget {
  final VacationModel vacation;

  const VacationCard({
    super.key,
    required this.vacation,
  });

  @override
  Widget build(BuildContext context) {
    final isApproved = vacation.isApproved == true;
    final isRejected = vacation.isApproved == false;
    final isPending = vacation.isApproved == null;

    return Column(
      children: [
        if (vacation.isPartVacation)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.smallPadding,
              vertical: AppSpaces.xSmallPadding,
            ),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppRadius.mediumRadius),
                  topRight: Radius.circular(AppRadius.mediumRadius),
                ),
                color: ColorManager.primaryColor),
            child: Text(
              context.tr.partVacation,
              style: context.whiteLabelMedium,
            ),
          ),
        Container(
          margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
          padding: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
            color: ColorManager.secondaryColor.withOpacity(.1),
          ),
          child: ListTile(
            title: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        vacation.userName,
                        style: context.whiteLabelMedium,
                        textDirection: TextDirection.ltr,
                      ),
                      context.smallGap,
                      if (vacation.isPartVacation)
                        Text(
                          '${context.tr.date}: ${vacation.fromDate.formatDateToString}\n${context.tr.fromTime}: ${vacation.partFromTime}\n${context.tr.toTime}: ${vacation.partToTime}',
                          style: context.whiteLabelMedium,
                        )
                      else
                        Text(
                          '${context.tr.fromDate}: ${vacation.fromDate.formatDateToString}\n${context.tr.toDate}: ${vacation.toDate.formatDateToString}',
                          style: context.whiteLabelMedium,
                        ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    context.smallGap,
                    Text(
                      isApproved
                          ? context.tr.approved
                          : isRejected
                              ? context.tr.rejected
                              : context.tr.pending,
                      style: context.labelLarge.copyWith(
                        color: isApproved
                            ? ColorManager.successColor
                            : isRejected
                                ? ColorManager.errorColor
                                : ColorManager.primaryColor,
                      ),
                    ),
                    if (isPending) ...[
                      context.largeGap,
                      if (UserModelHelper.isAdmin())
                        Row(
                          children: [
                            ApproveVacationButton(vacation: vacation),
                            ApproveVacationButton(
                                vacation: vacation, isReject: true),
                          ],
                        )
                    ],
                    // if (UserModelHelper.isAdmin()) ...[
                    //   context.mediumGap,
                    //   EmployeeVacationButton(vacation: vacation)
                    // ],
                  ],
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (vacation.reason.isNotEmpty) Text(vacation.reason),
                if (vacation.managerReason.isNotEmpty) ...[
                  context.xSmallGap,
                  Text(
                    '${context.tr.managerReason}: ${vacation.managerReason}',
                    style: context.whiteLabelMedium,
                  ),
                ],
              ],
            ),
            // trailing:
          ),
        ),
      ],
    );
  }
}
