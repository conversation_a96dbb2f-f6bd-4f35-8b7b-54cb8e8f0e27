import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class EmptyVacationsWidget extends StatelessWidget {
  const EmptyVacationsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          context.xxLargeGap,
          const CircleAvatar(
            radius: 50,
            backgroundColor: ColorManager.primaryColor,
            child: Icon(
              Icons.date_range_sharp,
              size: 50,
              color: Colors.white,
            ),
          ),
          context.largeGap,
          Text(
            context.tr.noVacationsFound,
            style: context.subHeadLine.copyWith(
              color: ColorManager.white,
            ),
          ),
        ],
      ),
    );
  }
}
