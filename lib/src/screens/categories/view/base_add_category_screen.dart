import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';

import '../../../core/resources/theme/theme.dart';
import 'add_and_edit_category/add_category.dart';
import 'add_and_edit_category/widgets/main_category_info_dialog_widget.dart';
import 'add_main_category/add_main_category.dart';

class BaseAddCategoryScreen extends HookWidget {
  const BaseAddCategoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final tabBarCtrl = useTabController(initialLength: 2);
    final mediaVM = context.read<MediaVM>();
    final categoryVM = context.watch<CategoryVM>();

    final tabBarList = [
      context.tr.category,
      context.tr.mainCategory,
    ];

    final selectedIndex = useState(0);
    return WillPopScope(
      onWillPop: () {
        context.read<MediaVM>().clearFiles();
        return Future.value(true);
      },
      child: Scaffold(
        appBar: MainAppBar(
          title: context.tr.addCategory,
          haveBackButton: true,
        ),
        body: ListView(
          children: [
            Container(
                margin: EdgeInsets.symmetric(
                    horizontal: AppSpaces.mediumPadding,
                    vertical: AppSpaces.smallPadding),
                padding: EdgeInsets.symmetric(vertical: AppSpaces.smallPadding),
                decoration: BoxDecoration(
                    color: ColorManager.textFieldColor(context),
                    borderRadius: BorderRadius.circular(AppRadius.baseRadius)),
                child: TabBar(
                    overlayColor: WidgetStateProperty.all(Colors.transparent),
                    controller: tabBarCtrl,
                    onTap: (value) {
                      mediaVM.clearFiles();
                      selectedIndex.value = value;

                      if (selectedIndex.value == 1 &&
                          !categoryVM.isThereOneMainCategory) {
                        showDialog(
                            context: context,
                            builder: (context) =>
                                MainCategoryInfoDialogWidget());
                      }
                    },
                    dividerColor: Colors.transparent,
                    indicatorColor: Colors.transparent,
                    tabs: tabBarList.indexed
                        .map((e) => AnimatedContainer(
                            duration: Duration(milliseconds: 300),
                            padding: EdgeInsets.symmetric(
                                horizontal: AppSpaces.xSmallPadding,
                                vertical: AppSpaces.smallPadding),
                            width: double.infinity,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: selectedIndex.value == e.$1
                                    ? ColorManager.primaryColor
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(
                                    AppRadius.baseRadius)),
                            child: Text(e.$2,
                                style: context.labelLarge.copyWith(
                                  color: selectedIndex.value == e.$1
                                      ? ColorManager.white
                                      : null,
                                  fontWeight: selectedIndex.value == e.$1
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ))))
                        .toList())),
            context.largeGap,
            _selectedScreen(selectedIndex.value)
          ],
        ),
      ),
    );
  }

  Widget _selectedScreen(int index) {
    switch (index) {
      case 0:
        return AddCategoryScreen();
      case 1:
        return AddMainCategoryScreen();
      default:
        return AddCategoryScreen();
    }
  }
}
