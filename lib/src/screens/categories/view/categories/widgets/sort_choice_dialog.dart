import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/sort_categories_only_screen.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/sort_main_categories_screen.dart';

class SortChoiceDialog extends StatelessWidget {
  final List<CategoryModel> categories;
  final List<MainCategoryModel> mainCategories;

  const SortChoiceDialog({
    super.key,
    required this.categories,
    required this.mainCategories,
  });

  @override
  Widget build(BuildContext context) {
    return BaseDialog(
      withCloseButton: true,
      child: Padding(
        padding: EdgeInsets.all(AppSpaces.mediumPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(context.tr.chooseCategoriesTypeYouWantToSort,
                style: context.subTitle, textAlign: TextAlign.center),
            context.mediumGap,

            // Main Categories Button
            SizedBox(
              width: double.infinity,
              height: 35.h,
              child: Button(
                label: context.tr.sortMainCategories,
                fontSize: 16,
                radius: AppRadius.baseRadius,
                haveElevation: false,
                onPressed: () {
                  Navigator.pop(context);
                  context.to(SortMainCategoriesScreen(
                    mainCategories: mainCategories,
                  ));
                },
                color: ColorManager.primaryColor,
              ),
            ),

            context.smallGap,

            // Categories Button
            SizedBox(
              width: double.infinity,
              height: 35.h,
              child: Button(
                label: context.tr.sortCategories,
                haveElevation: false,
                fontSize: 16,
                radius: AppRadius.baseRadius,
                onPressed: () {
                  Navigator.pop(context);
                  context.to(SortCategoriesOnlyScreen(
                    categories: categories,
                  ));
                },
                color: ColorManager.secondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
