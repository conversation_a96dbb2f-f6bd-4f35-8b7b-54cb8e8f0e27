import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:provider/provider.dart';
import 'package:zo_animated_border/widget/zo_snake_border.dart';

import '../../../../../../../../../core/resources/theme/theme.dart';
import '../../../../../../../../auth/models/helper_models/vendor_helper_model.dart';
import '../../../../../../../../auth/view_model/auth_view_model.dart';
import '../../../../../../../../subscription_plans/view/vendor_choose_plan_screen.dart';

class ExpiredVendorCardWidget extends HookWidget {
  const ExpiredVendorCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () => context.to(const VendorChoosePlanScreen()),
          child: ZoSnakeBorder(
            duration: 5,
            glowOpacity: .2,
            snakeHeadColor: Colors.white,
            snakeTailColor: Colors.white.withOpacity(.4),
            snakeTrackColor: Colors.transparent,
            borderWidth: 2,
            borderRadius: BorderRadius.circular(10),
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(AppSpaces.smallPadding),
              decoration: BoxDecoration(
                color: Color(0x5ed9d9d9),
                borderRadius: BorderRadius.circular(AppRadius.baseRadius),
              ),
              child: Text(
                context.tr.renewNow,
                style: context.whiteLabelLarge,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
