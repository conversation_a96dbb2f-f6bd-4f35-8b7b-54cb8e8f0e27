import 'dart:io';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/model/subscription_request_model.dart';

class SubscriptionRequestRepository {
  final BaseApiServices networkApiServices;

  SubscriptionRequestRepository({required this.networkApiServices});

  Future<void> createSubscriptionRequest(
      {required SubscriptionRequestModel subscriptionRequest,
      required String fileResult,
      bool withOutVendor = false}) async {
    try {
      await networkApiServices.postResponse(ApiEndPoints.subscriptionRequests,
          fieldName: "payment_attachment",
          fileResult: [fileResult],
          connectWithVendorRelation: false,
          data: subscriptionRequest.toJson(),
          withOutVendor: withOutVendor);
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  // * Approve Subscription Request
  Future<void> approveSubscriptionRequest(
      {required SubscriptionRequestModel subscriptionRequest}) async {
    try {
      await networkApiServices.putResponse(
        ApiEndPoints.subscriptionRequests,
        data: {
          ApiStrings.documentId: subscriptionRequest.documentId,
          ApiStrings.paidAmount: subscriptionRequest.pricingModel?.price,
          ApiStrings.isApproved: true,
        },
      );
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  // * Get Subscription Requests
  Future<List<SubscriptionRequestModel>> getSubscriptionRequests() async {
    try {
      final response = await networkApiServices.getResponse(
        ApiEndPoints.subscriptionRequests,
        withOutVendor: true,
      );

      final data = List<SubscriptionRequestModel>.from(
          response.map((x) => SubscriptionRequestModel.fromJson(x)));

      return data;
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  // * Delete Subscription Request
  Future<void> deleteSubscriptionRequest(String? documentId) async {
    try {
      await networkApiServices.deleteResponse(
        "${ApiEndPoints.subscriptionRequests}/$documentId",
      );
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }
}
