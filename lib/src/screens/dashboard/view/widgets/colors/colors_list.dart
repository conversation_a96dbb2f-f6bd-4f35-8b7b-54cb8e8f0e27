import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_spaces.dart';
import '../../../../../core/shared_widgets/loading/loading_widget.dart';
import '../../../view_model/extra_settings_view_model.dart';
import 'color_card.dart';

class ColorsList extends StatelessWidget {
  const ColorsList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ExtraSettingVM>(
      builder: (context, extraSettingsVM, child) {
        if (extraSettingsVM.isLoading) {
          return const Expanded(child: LoadingWidget());
        }
        if (extraSettingsVM.extraSettings?.colors?.isEmpty ?? true) {
          return Expanded(
            child: EmptyDataWidget(
              message: context.tr.noColorsAdded,
            ),
          );
        }
        return Expanded(
          child: ListView.separated(
              shrinkWrap: true,
              separatorBuilder: (context, index) {
                return context.largeGap;
              },
              padding:
                  const EdgeInsets.symmetric(vertical: AppSpaces.mediumPadding),
              itemCount: extraSettingsVM.extraSettings?.colors?.length ?? 0,
              itemBuilder: (context, index) {
                final color = extraSettingsVM.extraSettings!.colors?[index];

                return ColorCard(
                  color: color ?? ExtraSettingsModel(),
                  extraSettingsVM: extraSettingsVM,
                );
              }),
        );
      },
    );
  }
}
