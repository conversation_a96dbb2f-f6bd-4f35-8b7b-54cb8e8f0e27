import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';

class SettingsModel {
  final String termsAr;
  final String termsEn;
  final String aboutUsAr;
  final String aboutUsEn;
  final String? version;
  final ContactUsModel? contactUs;
  final bool checkUpdate;
  final List<TemplateModel>? templates;

  SettingsModel({
    this.termsAr = '',
    this.termsEn = '',
    this.aboutUsAr = '',
    this.aboutUsEn = '',
    this.contactUs,
    this.version,
    this.checkUpdate = false,
    this.templates,
  });

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    final contactUs = json[ApiStrings.contactUs] != null
        ? ContactUsModel.fromJson(json[ApiStrings.contactUs])
        : null;

    final templates = json[ApiStrings.templates] != null
        ? (json[ApiStrings.templates] as List)
            .map((template) => TemplateModel.fromJson(template))
            .toList()
        : null;

    return SettingsModel(
      termsAr: json[ApiStrings.termsAr] ?? '',
      termsEn: json[ApiStrings.termsEn] ?? '',
      aboutUsAr: json[ApiStrings.aboutAr] ?? '',
      aboutUsEn: json[ApiStrings.aboutEn] ?? '',
      version: json[ApiStrings.version] ?? '',
      checkUpdate: json[ApiStrings.checkUpdate] ?? false,
      contactUs: contactUs,
      templates: templates,
    );
  }

  factory SettingsModel.empty() => SettingsModel(
        termsAr: '',
        termsEn: '',
        aboutUsAr: '',
        checkUpdate: false,
        contactUs: ContactUsModel.empty(),
        version: '',
        aboutUsEn: '',
      );
}

class TemplateModel {
  final int? id;
  final String name;
  final String url;

  TemplateModel({this.id, required this.name, this.url = ''});

  String get nameAr {
    if (name == "Clothes") return "الملابس";
    if (name == "Accessories") return "الإكسسوارات";
    if (name == "digital") return "الرقمي";
    if (name == "Gifts") return "الهدايا";
    if (name == "Medical") return "الطبي";
    if (name == "Market") return "الماركت";
    if (name == "Others") return "أخرى";
    if (name == "Default") return "الإفتراضي";
    return name;
  }

  bool get isDefault {
    return name == "Default";
  }

  // bool get isNotOthers {
  //   return name != "Others";
  // }

  factory TemplateModel.fromJson(Map<String, dynamic> json) {
    return TemplateModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.name] ?? '',
      url: json[ApiStrings.url] ?? '',
    );
  }

  factory TemplateModel.empty() => TemplateModel(id: 0, name: '', url: '');
}

class ContactUsModel {
  final ContactUsFiled? email;
  final ContactUsFiled? website;
  final ContactUsFiled? facebook;
  final ContactUsFiled? tiktok;
  final ContactUsFiled? instagram;
  final ContactUsFiled? whatsapp;
  final ContactUsFiled? youtube;

  ContactUsModel(
      {this.email,
      this.facebook,
      this.website,
      this.youtube,
      this.tiktok,
      this.whatsapp,
      this.instagram});

  factory ContactUsModel.fromJson(Map<String, dynamic> json) {
    Log.w('json $json');
    return ContactUsModel(
      whatsapp: ContactUsFiled.fromJson(json[ApiStrings.whatsapp] ?? ''),
      website: ContactUsFiled.fromJson(json[ApiStrings.website] ?? ''),
      youtube: ContactUsFiled.fromJson(json[ApiStrings.youtube] ?? ''),
      email: ContactUsFiled.fromJson(json[ApiStrings.email] ?? ''),
      facebook: ContactUsFiled.fromJson(json[ApiStrings.facebook] ?? ''),
      tiktok: ContactUsFiled.fromJson(json[ApiStrings.tiktok] ?? ''),
      instagram: ContactUsFiled.fromJson(json[ApiStrings.instagram] ?? ''),
    );
  }

  // empty constructor
  factory ContactUsModel.empty() => ContactUsModel(
        email: ContactUsFiled.empty(),
        facebook: ContactUsFiled.empty(),
        tiktok: ContactUsFiled.empty(),
        instagram: ContactUsFiled.empty(),
        whatsapp: ContactUsFiled.empty(),
        youtube: ContactUsFiled.empty(),
      );
}

class ContactUsFiled {
  final String? name;
  final String? url;

  ContactUsFiled({this.name, this.url});

  factory ContactUsFiled.fromJson(Map<String, dynamic> json) {
    return ContactUsFiled(
      name: json[ApiStrings.name],
      url: json[ApiStrings.url],
    );
  }

  factory ContactUsFiled.empty() => ContactUsFiled(name: '', url: '');
}
