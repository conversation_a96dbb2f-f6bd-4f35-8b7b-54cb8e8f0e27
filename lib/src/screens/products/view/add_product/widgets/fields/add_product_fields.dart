import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/alert_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/inventory/manage_product_colors_and_Sizes.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/product_fields/product_basic_info.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/product_fields/product_status.dart';

import '../../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../../core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import '../../../../../categories/models/category_model.dart';
import '../../../../../dashboard/models/extra_setting_model.dart';
import 'inventory/inventory_container_widget.dart';

class AddProductsFields extends HookWidget {
  final (
    Map<String, TextEditingController> controllers,
    Map<String, ValueNotifier> valueNotifiers,
  ) fields;
  final ValueNotifier<bool> isVendorNeedInventory;
  final ProductModel? product;
  final CategoryModel category;

  const AddProductsFields({
    super.key,
    required this.fields,
    required this.product,
    required this.isVendorNeedInventory,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    final isSizeColorInventory =
        fields.$2[ApiStrings.isSizeColorInventory] as ValueNotifier<bool>;
    final fieldsController = fields.$1;

    final valueNotifiers = fields.$2;

    final inventoryEnabled = valueNotifiers[ApiStrings.inventoryEnabled];

    final selectedSizes = valueNotifiers[ApiStrings.sizes]
        as ValueNotifier<List<ExtraSettingsModel>>;

    final priceIcon = Icon(
      CupertinoIcons.money_dollar,
      color: context.isDark ? CupertinoColors.white : CupertinoColors.black,
    );

    final colors =
        valueNotifiers[ApiStrings.colors]?.value as List<ExtraSettingsModel>;
    final sizes =
        valueNotifiers[ApiStrings.sizes]?.value as List<ExtraSettingsModel>;

    final isEdit = product != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProductStatus(
          valueNotifiers: valueNotifiers,
        ),

        context.fieldsGap,

        ProductBasicInfo(
          controllers: fieldsController,
        ),

        context.fieldsGap,

        //! Price
        ValueListenableBuilder(
          valueListenable: selectedSizes,
          builder: (context, selectedSizesValue, __) {
            final enabled =
                selectedSizesValue.every((element) => element.price == null);

            return Row(
              children: [
                Expanded(
                  child: BaseTextField(
                    onTap: () {
                      if (!enabled) {
                        context.showBarMessage(
                          context.tr
                              .youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice,
                          isError: true,
                        );
                      }
                    },
                    withoutEnter: false,
                    enabled: enabled,
                    controller: fieldsController[ApiStrings.price],
                    icon: priceIcon,
                    textInputType: TextInputType.number,
                    title: context.tr.price,
                  ),
                ),

                context.smallGap,

                //! Sale price field
                Expanded(
                  child: BaseTextField(
                    enabled: enabled,
                    onTap: () {
                      if (!enabled) {
                        context.showBarMessage(
                          context.tr
                              .youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice,
                          isError: true,
                        );
                      }
                    },
                    isRequired: false,
                    withoutEnter: false,
                    controller: fieldsController[ApiStrings.salePrice],
                    icon: Icon(
                      Icons.discount_outlined,
                      size: AppSpaces.iconSize,
                      color: context.isDark ? Colors.white : Colors.black,
                    ),
                    textInputType: TextInputType.number,
                    title: context.tr.salePrice,
                  ),
                ),
              ],
            );
          },
        ),

        context.fieldsGap,

        Container(
          padding: EdgeInsets.symmetric(
              horizontal: AppSpaces.mediumPadding,
              vertical: AppSpaces.smallPadding),
          decoration: BoxDecoration(
              color: ColorManager.textFieldColor(context),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.baseRadius),
                topRight: Radius.circular(AppRadius.baseRadius),
                bottomLeft: Radius.circular(
                    isVendorNeedInventory.value ? 0 : AppRadius.baseRadius),
                bottomRight: Radius.circular(
                    isVendorNeedInventory.value ? 0 : AppRadius.baseRadius),
              )),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${context.tr.inventory} - ${isVendorNeedInventory.value ? context.tr.on : context.tr.off}',
                style: context.labelLarge,
              ),
              SwitchButtonWidget(
                  value: isVendorNeedInventory,
                  onChanged: (value) async {
                    inventoryEnabled?.value = value;
                    isVendorNeedInventory.value = value;
                  }),
            ],
          ),
        ),

        if (isVendorNeedInventory.value) ...[
          //! Inventory
          Column(
            children: [
              InventoryContainerWidget(
                child: Row(
                  children: [
                    InventoryToggleButton(
                      title: context.tr.productStock,
                      isSelected: !isSizeColorInventory.value,
                      onTap: () async {
                        if (isEdit) {
                          await showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialogWidget(
                                isWarningMessage: true,
                                onConfirm: () {
                                  for (var color in colors) {
                                    color.stock = null;
                                  }
                                  for (var size in sizes) {
                                    size.stock = null;
                                  }

                                  fieldsController[ApiStrings.inventory]
                                      ?.clear();

                                  isSizeColorInventory.value = false;

                                  valueNotifiers[
                                          ApiStrings.isSizeColorInventory]
                                      ?.value = false;

                                  context.back();
                                },
                                child: Text(
                                    context.tr
                                        .ifYouTurnOnTheInventoryYouWillRemoveSizeAndColorsStock,
                                    style: context.labelLarge),
                              );
                            },
                          );
                        } else {
                          isSizeColorInventory.value = false;
                          valueNotifiers[ApiStrings.isSizeColorInventory]
                              ?.value = false;
                        }
                      },
                    ),
                    InventoryToggleButton(
                      title: context.tr.sizesAndColors,
                      isSelected: isSizeColorInventory.value,
                      onTap: () async {
                        if (isEdit) {
                          await showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialogWidget(
                                isWarningMessage: true,
                                onConfirm: () {
                                  fieldsController[ApiStrings.inventory]
                                      ?.clear();

                                  isSizeColorInventory.value = true;

                                  valueNotifiers[
                                          ApiStrings.isSizeColorInventory]
                                      ?.value = true;

                                  context.back();
                                },
                                child: Text(
                                    context.tr
                                        .ifYouTurnOofTheInventoryYouWillRemoveTheProductStock,
                                    style: context.labelLarge),
                              );
                            },
                          );
                        } else {
                          isSizeColorInventory.value = true;
                          valueNotifiers[ApiStrings.isSizeColorInventory]
                              ?.value = true;
                        }
                      },
                    ),
                  ],
                ),
              ),
              if (!isSizeColorInventory.value) ...[
                context.mediumGap,
                BaseTextField(
                  isRequired: isVendorNeedInventory.value,
                  controller: fieldsController[ApiStrings.inventory],
                  hint: context.tr.inventory,
                  textInputType: TextInputType.number,
                  icon: Icon(
                    Icons.inventory_outlined,
                    size: AppSpaces.iconSize,
                    color: context.isDark ? Colors.white : Colors.black,
                  ),
                ),
              ]
            ],
          ),
        ],

        context.fieldsGap,

        Button(
          radius: AppRadius.baseRadius,
          color: ColorManager.textFieldColor(context),
          haveElevation: false,
          isBold: false,
          isWhiteText: context.isDark ? true : false,
          label: context.tr.manageSizesAndColors,
          onPressed: () => context.to(ManageProductColorsAndSizes(
            isVendorNeedInventory: isVendorNeedInventory.value,
            category: category,
            valueNotifiers: valueNotifiers,
            controllers: fields.$1,
            canAddStock:
                isSizeColorInventory.value && isVendorNeedInventory.value,
          )),
        ),
      ],
    );
  }
}
