import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/buttons/base_text_button.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/sizes/choose_size_card.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../../categories/models/category_model.dart';
import '../../../../../../dashboard/view/size_and_color_screen.dart';
import '../../../../../../dashboard/view_model/extra_settings_view_model.dart';

class ProductSizesList extends StatelessWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final Map<String, TextEditingController>? controllers;
  final List<ExtraSettingsModel>? productSize;
  final CategoryModel category;
  final bool isSingle;
  final bool canAddStock;
  final Function(ExtraSettingsModel size)? onSelected;

  const ProductSizesList({
    super.key,
    required this.valueNotifiers,
    required this.category,
    this.controllers,
    this.onSelected,
    this.isSingle = false,
    this.canAddStock = true,
    this.productSize,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ExtraSettingVM>(
      builder: (context, extraSettingsVM, child) {
        if (extraSettingsVM.isLoading) {
          return const LoadingWidget();
        }

        final selectedSizes = valueNotifiers[ApiStrings.sizes]
            as ValueNotifier<List<ExtraSettingsModel>>;

        final sizes = productSize ??
            extraSettingsVM.extraSettings?.sizes.where((element) => element
                .categories
                .map((e) => e.documentId)
                .contains(category.documentId)) ??
            [];

        if (sizes.isEmpty) {
          return Column(
            children: [
              EmptyDataWidget(
                message: context.tr.noSizesAdded,
              ),
              BaseTextButton(
                  title: context.tr.addSizes,
                  withUnderline: true,
                  onTap: () => context.to(const SizeAndColorScreen())),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(context.tr.sizes, style: context.title),
                context.smallGap,
                const _AddSizesButton(),
              ],
            ),
            context.mediumGap,
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                vertical: AppSpaces.xSmallPadding,
              ),
              decoration: BoxDecoration(
                color: ColorManager.textFieldColor(context),
                borderRadius: BorderRadius.circular(
                  AppRadius.baseRadius,
                ),
              ),
              child: Wrap(
                children: sizes.mapIndexed((index, size) {
                  final isSizeSelected = selectedSizes.value
                      .any((e) => e.englishName == size.englishName);

                  return ChooseSizeCard(
                    size: size,
                    isSelected: isSizeSelected,
                  ).onTap(() {
                    if (!isSingle) {
                      size.price = null;
                    }

                    extraSettingsVM.selectOrUnSelectSize(
                      selectedSizes: selectedSizes,
                      size: productSize?.firstWhereOrNull(
                              (e) => e.englishName == size.englishName) ??
                          size,
                      isSingle: isSingle,
                    );

                    if (onSelected != null) {
                      onSelected!(size);
                    }

                    if (isSizeSelected) {
                      _updatePriceFields(
                          selectedSizes.value, controllers, context);
                    }
                  }).paddingSymmetric(
                    horizontal: AppSpaces.smallPadding,
                    vertical: AppSpaces.xSmallPadding,
                  );
                }).toList(),
              ),
            ),
            context.mediumGap,
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: selectedSizes.value.length,
              itemBuilder: (context, index) {
                final size = selectedSizes.value[index];

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.tr.size}: ${size.englishName ?? ''}',
                      textAlign: TextAlign.center,
                      style: context.title.copyWith(
                        color: context.isDark
                            ? ColorManager.white
                            : ColorManager.black,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    context.smallGap,
                    Column(
                      children: [
                        BaseTextField(
                          isRequired: true,
                          title: context.tr.price,
                          textInputType: TextInputType.number,
                          initialValue: size.price?.toString() ?? '',
                          hint: context.tr.price,
                          onChanged: (value) {
                            controllers?[ApiStrings.salePrice]?.text = '';

                            size.price = num.tryParse(value);
                            selectedSizes.value
                                .where((e) => e.englishName == size.englishName)
                                .forEach((element) {
                              element.price = size.price;
                            });
                            _updatePriceFields(
                                selectedSizes.value, controllers, context);
                          },
                        ),
                        context.smallGap,
                        BaseTextField(
                          initialValue: canAddStock
                              ? size.stock?.toString() ?? '0'
                              : context.tr.sizeStockIInventoryIsOff,
                          title: context.tr.stock,
                          hint: canAddStock ? context.tr.stock : null,
                          textInputType: TextInputType.number,
                          onChanged: (value) {
                            size.stock = int.tryParse(value);
                            selectedSizes.value
                                .where((e) => e.englishName == size.englishName)
                                .forEach((element) {
                              element.stock = size.stock;
                            });
                            context.read<ProductVM>().notifyListeners();
                          },
                          enabled: canAddStock,
                        ),
                      ],
                    ),
                    context.smallGap,
                  ],
                ).paddingSymmetric(
                  horizontal: AppSpaces.smallPadding,
                  vertical: AppSpaces.xSmallPadding,
                );
              },
              separatorBuilder: (context, index) => const Divider(),
            ),
          ],
        );
      },
    );
  }

  void _updatePriceFields(List<ExtraSettingsModel> selectedSizes,
      Map<String, TextEditingController>? controllers, BuildContext context) {
    selectedSizes.sort((a, b) {
      if (a.price == null && b.price == null) return 0;
      if (a.price == null) return 1;
      if (b.price == null) return -1;
      return a.price!.compareTo(b.price!);
    });

    final firstPrice = selectedSizes.firstWhereOrNull((e) => e.price != null);

    controllers?[ApiStrings.price]?.text = firstPrice?.price?.toString() ?? '';

    valueNotifiers[ApiStrings.isSale]?.value = false;

    context.read<ProductVM>().notifyListeners();
  }
}

class _AddSizesButton extends StatelessWidget {
  const _AddSizesButton();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.to(const SizeAndColorScreen(selectedTabIndex: 0)),
      child: CircleAvatar(
        radius: 12,
        backgroundColor: ColorManager.primaryColor,
        child: Icon(
          Icons.add,
          color: ColorManager.white,
          size: 18.r,
        ),
      ),
    );
  }
}
