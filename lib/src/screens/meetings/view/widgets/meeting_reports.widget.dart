import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/meetings/models/meeting_model.dart';
import 'package:xr_helper/xr_helper.dart';

class MeetingReportsWidget extends HookConsumerWidget {
  final ValueNotifier<DateTime> selectedFromDate;
  final ValueNotifier<DateTime> selectedToDate;
  final ValueNotifier<MeetingModel?> selectedMeeting;

  const MeetingReportsWidget({
    super.key,
    required this.selectedFromDate,
    required this.selectedToDate,
    required this.selectedMeeting,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDates = (selectedFromDate, selectedToDate);

    final generatedReport = useState<String>('');

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      child: Column(
        children: [
          // * Date Filter
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(context.tr.fromDate, style: context.whiteSubTitle),
                    context.smallGap,
                    BaseDatePicker(
                      selectedDateNotifier: selectedDates.$1,
                      label: context.tr.fromDate,
                      isWhite: true,
                    ),
                  ],
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(context.tr.toDate, style: context.whiteSubTitle),
                    context.smallGap,
                    BaseDatePicker(
                      selectedDateNotifier: selectedDates.$2,
                      label: context.tr.toDate,
                      isWhite: true,
                    ),
                  ],
                ),
              ),
            ],
          ),

          // context.largeGap,

          // * Meeting Dropdown
          // MeetingsDropDown(
          //   label: context.tr.meeting,
          //   selectedMeeting: selectedMeeting,
          // )
        ],
      ),
    );
  }
}
