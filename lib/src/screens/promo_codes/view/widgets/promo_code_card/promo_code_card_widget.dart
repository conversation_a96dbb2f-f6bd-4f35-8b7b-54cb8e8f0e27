import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/promo_codes/controller/promo_codes_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_radius.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/dialogs/show_dialog.dart';
import '../../../model/promo_codes_model.dart';
import '../add_promo_codes_dialog.dart';

class PromoCodesCardWidget extends HookWidget {
  final PromoCodesModel promoCode;

  const PromoCodesCardWidget({
    super.key,
    required this.promoCode,
  });

  @override
  Widget build(BuildContext context) {
    final promoCodesVM = context.read<PromoCodesVM>();

    final isActive = useState(promoCode.isActive);

    return Container(
      // padding: EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
          color: context.appTheme.cardColor,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: context.isDark
              ? ConstantsWidgets.darkBoxShadowFromBottom
              : ConstantsWidgets.boxShadowFromBottom),
      child: Column(
        children: [
          ListTile(
            selectedTileColor: context.appTheme.cardColor,
            tileColor: context.appTheme.cardColor,
            shape: RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
            ),
            title: Text(
              promoCode.promo,
              style: context.subTitle,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  promoCode.isPercent
                      ? '${promoCode.discount}%'
                      : promoCode.discount.toString().toCurrency(context),
                  style: context.subTitle.copyWith(
                    color: ColorManager.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    Icon(
                      promoCode.showInList ? Icons.check_circle : Icons.cancel,
                      color: promoCode.showInList
                          ? ColorManager.successColor
                          : ColorManager.errorColor,
                    ),
                    context.xSmallGap,
                    Text(
                      context.tr.showInList,
                      style: context.labelLarge,
                    ),
                  ],
                ),
              ],
            ),
            leading: SwitchButtonWidget(
              value: isActive,
              onChanged: (val) {
                isActive.value = val;
                promoCodesVM.editPromoCodesStatus(
                  context,
                  isActive: val,
                  documentId: promoCode.documentId!,
                );
              },
            ),
            trailing: SizedBox(
              width: kIsWeb ? 80 : 90.w,
              child: Row(
                children: [
                  const CircleAvatar(
                          backgroundColor: ColorManager.successColor,
                          child: BaseLottieWidget.icon(
                              'assets/animated/edit.json',
                              width: 20,
                              height: 20))
                      .onTapWithRipple(() {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AddPromoCodesDialog(
                          promoCode: promoCode,
                        );
                      },
                    );
                  }),

                  context.smallGap,

                  //? Delete Button
                  const CircleAvatar(
                          backgroundColor: ColorManager.errorColor,
                          child: BaseLottieWidget.icon(Assets.animatedDelete,
                              width: 20, height: 20))
                      .onTapWithRipple(() {
                    showAlertDialog(context,
                        child: Text(
                            context.tr.areYouSureYouWantToDeleteThisPromoCode,
                            style: context.labelLarge), onConfirm: () async {
                      final promoCodesVM = context.read<PromoCodesVM>();

                      await promoCodesVM.deletePromoCodes(
                        context,
                        promoCode: promoCode,
                      );
                    });
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
