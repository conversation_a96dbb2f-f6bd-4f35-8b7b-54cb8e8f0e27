import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/local/shared_preferences/get_storage.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/main_screen.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/filter_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/order_repository.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model_helper.dart';

import '../../store/view/store_screen.dart';
import '../models/order/order_model_helper.dart';
import '../models/order/orders_statistics_model.dart';

class OrderVM extends OrderVMHelper {
  final OrderRepo _orderRepo;

  OrderVM(this._orderRepo) : super(_orderRepo);

  var orders = <OrderModel>[];
  var homeOrders = <OrderModel>[];
  var orderStatistics = OrderStatisticsModel();
  num storeTotal = 0;
  bool isPaid = false;

  // Selection state for multi-select functionality
  var selectedOrders = <OrderModel>[];
  var selectedHomeOrders = <OrderModel>[];

  bool get hasSelectedOrders =>
      selectedOrders.isNotEmpty || selectedHomeOrders.isNotEmpty;

  void _setTotal() {
    storeTotal = storeOrder
        .where((order) => order.status == OrderStatus.done)
        .fold(0, (previousValue, element) => previousValue + element.total!);
  }

  int get storeOrdersCount => storeOrder.length;

  FilterModel _filterModel = FilterModel();

  FilterModel get orderFilter => _filterModel;

  void setOrderFilter(BuildContext context,
      {required FilterModel filter, bool fetchOrders = true}) {
    _filterModel = filter;

    if (fetchOrders) {
      getOrders(context);
    }
  }

  List<OrderModel> get storeOrder =>
      orders.where((order) => order.isFromStore == true).toList();

  List<OrderModel> get appOrders =>
      orders.where((order) => order.isFromStore == false).toList();

  Future<List<OrderModel>> getOrders(
    BuildContext context, {
    int? limit,
  }) async {
    return await baseFunction(context, () async {
      final filter = _filterModel.copyWith(
        limit: limit ?? _filterModel.limit,
      );

      final allOrders = (await _orderRepo.getOrders(
        orderFilter: filter,
      ))
          .toList();

      orders = allOrders;

      //? Set total
      _setTotal();

      return orders;
    });
  }

  Future<OrderStatisticsModel> getOrderStatistics(
    BuildContext context,
  ) async {
    return await baseFunction(context, () async {
      orderStatistics = await _orderRepo.getOrderStatistics();

      return orderStatistics;
    });
  }

  Future<void> getHomeOrders(
    BuildContext context,
  ) async {
    return await baseFunction(context, () async {
      final filter = _filterModel.copyWith(
        limit: 20,
      );

      homeOrders = (await _orderRepo.getHomeOrders(
        orderFilter: filter,
      ))
          .toList();
    });
  }

  Future<void> editOrder(
    BuildContext context, {
    required OrderModel order,
    required OrderModel mainOrder,
  }) async {
    await baseFunction(
        context,
        () async {
          await _orderRepo.editOrder(orderModel: order);

          if (!context.mounted) return;

          final isStatusChanged = mainOrder.status != order.status;

          final isShippingChanged =
              mainOrder.deliveryCost != order.deliveryCost;

          //? Send order status notification
          if (isStatusChanged) {
            sendOrderStatus(context, order: order);
          }

          // ? Send delivery cost notification
          if (isShippingChanged) {
            sendDeliveryCost(context, order: order);
          }
        },
        type: FlushBarType.update,
        isBack: true,
        additionalFunction: (BuildContext context) {
          getOrders(context);
        });
  }

//! Store Order ================================
  Future<void> makeStoreOrder(
    BuildContext context, {
    required OrderModel order,
  }) async {
    await baseFunction(
        context,
        () async {
          await _orderRepo.makeStoreOrder(orderModel: order);
        },
        isBack: false,
        additionalFunction: (BuildContext context) {
          GetStorageHandler.removeKey(key: ApiStrings.cart);
          context.toReplacement(const StoreScreen());
          context.showBarMessage(context.tr.checkedOutSuccessfully);
        });
  }

//! Cancel Order ================================
  Future<void> cancelOrder(BuildContext context,
      {required OrderModel order}) async {
    await baseFunction(context, () async {
      await _orderRepo.editOrder(orderModel: order);

      if (!context.mounted) return;
      getOrders(context);

//? Send notification
      sendOrderStatus(context, order: order);
    },
        type: FlushBarType.update,
        additionalFunction: (_) => context.toReplacement(const MainScreen()));
  }

  void clearFilter() {
    _filterModel = FilterModel(searchBy: OrdersSearchBy.orderId);

    Log.f('ClearFilter');

    notifyListeners();
  }

  void clearAllOrdersAndFilters() {
    orders.clear();
    homeOrders.clear();
    orderStatistics = OrderStatisticsModel();
    storeTotal = 0;
    selectedOrders.clear();
    selectedHomeOrders.clear();
    _filterModel = FilterModel(searchBy: OrdersSearchBy.orderId);
    notifyListeners();
  }

  // Selection methods for multi-select functionality
  void toggleOrderSelection(OrderModel order) {
    // Check if it's a home order or regular order
    if (homeOrders.contains(order)) {
      if (selectedHomeOrders.contains(order)) {
        selectedHomeOrders.remove(order);
      } else {
        selectedHomeOrders.add(order);
      }
    } else {
      if (selectedOrders.contains(order)) {
        selectedOrders.remove(order);
      } else {
        selectedOrders.add(order);
      }
    }
    notifyListeners();
  }

  bool isOrderSelected(OrderModel order) {
    return selectedOrders.contains(order) || selectedHomeOrders.contains(order);
  }

  void clearSelection() {
    selectedOrders.clear();
    selectedHomeOrders.clear();
    notifyListeners();
  }

  void selectAllOrders() {
    selectedOrders.clear();
    selectedOrders.addAll(orders);
    notifyListeners();
  }

  void selectAllHomeOrders() {
    selectedHomeOrders.clear();
    selectedHomeOrders.addAll(homeOrders);
    notifyListeners();
  }

  // Bulk invoice generation
  Future<String> generateBulkOrderInvoices(BuildContext context) async {
    return await baseFunction(context, () async {
      // Combine both selected orders and selected home orders
      final allSelectedOrders = [...selectedOrders, ...selectedHomeOrders];

      final generatedPDFReportPath = await _orderRepo
          .generateBulkOrderInvoices(context, allOrders: allSelectedOrders);

      return generatedPDFReportPath;
    });
  }
}
