import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:xr_helper/xr_helper.dart';

class KPICalculateWidget extends StatelessWidget {
  final List<TaskModel> tasks;

  const KPICalculateWidget({super.key, required this.tasks});

  @override
  Widget build(BuildContext context) {
    final filteredTasks = tasks
        .where((task) =>
            task.isParentTask == false &&
            tasks.map((e) => e.id).contains(task.parentId))
        .toList();

    int closedTasksCount =
        filteredTasks.where((task) => task.isCompleted).length;

    // Calculate the percentage of closed tasks
    double closedTasksPercentage =
        (closedTasksCount / filteredTasks.length) * 100;

    // Decide which icon to display
    Widget kpiIcon = CircleAvatar(
      maxRadius: 15,
      backgroundColor: closedTasksPercentage > 85
          ? ColorManager.successColor
          : ColorManager.errorColor,
      child: Icon(
        closedTasksPercentage > 85 ? Icons.check : Icons.close,
        color: Colors.white,
      ),
    );

    return Row(
      children: [
        Text(
          '${closedTasksPercentage.isNaN ? 0 : closedTasksPercentage.toStringAsFixed(0)}%',
          style: context.subHeadLine.copyWith(
            color: ColorManager.white,
          ),
        ),
        context.smallGap,
        kpiIcon,
      ],
    ).paddingSymmetric(
      horizontal: AppSpaces.smallPadding,
      vertical: AppSpaces.xSmallPadding,
    );
  }
}
