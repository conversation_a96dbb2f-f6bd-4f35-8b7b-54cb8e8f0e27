import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/providers/tasks_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class DeleteTaskDialog extends HookConsumerWidget {
  final TaskModel task;

  const DeleteTaskDialog({
    super.key,
    required this.task,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final taskController = ref.watch(taskControllerNotifierProvider(context));

    void deleteTask() async {
      if (task.isApproved == true) {
        context.showBarMessage(context.tr.cannotDeleteApprovedTask,
            isError: true);
        return;
      }

      await taskController.deleteTask(task: task);
    }

    if (taskController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = context.tr.deleteTask;

    final description = context.tr.areYouSureYouWantToDeleteThisTask;

    final buttonLabel = context.tr.delete;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to close this task?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: deleteTask,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
