import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/providers/employee_requests_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class DeleteEmployeeRequestDialog extends HookConsumerWidget {
  final EmployeeRequestModel request;
  final bool isEmployeeRequest;

  const DeleteEmployeeRequestDialog({
    super.key,
    required this.request,
    this.isEmployeeRequest = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final employeeRequestController =
        ref.watch(employeeRequestControllerNotifierProvider(context));

    void deleteEmployeeRequest() async {
      await employeeRequestController.deleteEmployeeRequest(
          employeeRequest: request);
    }

    if (employeeRequestController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = isEmployeeRequest
        ? context.tr.deleteEmployeeRequest
        : context.tr.deleteRequest;

    final description = isEmployeeRequest
        ? context.tr.areYouSureYouWantToDeleteThisEmployeeRequest
        : context.tr.areYouSureYouWantToDeleteThisRq;

    final buttonLabel = context.tr.delete;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to close this employeeRequest?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: deleteEmployeeRequest,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
