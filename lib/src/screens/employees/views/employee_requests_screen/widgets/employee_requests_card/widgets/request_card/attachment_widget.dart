import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeRequestAttachmentWidget extends StatelessWidget {
  final EmployeeRequestModel request;

  const EmployeeRequestAttachmentWidget({super.key, required this.request});

  @override
  Widget build(BuildContext context) {
    return request.attachmentUrl != null && request.attachmentUrl!.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.only(top: AppSpaces.smallPadding),
            child: ClipRRect(
                borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                child: Image.network(
                  request.attachmentUrl!,
                  fit: BoxFit.cover,
                  height: 100,
                  width: 100,
                ).onTap(() {
                  showDialog(
                    context: context,
                    builder: (_) => Dialog(
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppRadius.smallRadius),
                      ),
                      child: ClipRRect(
                        borderRadius:
                            BorderRadius.circular(AppRadius.smallRadius),
                        child: Image.network(
                          request.attachmentUrl!,
                          fit: BoxFit.cover,
                          height: context.height * 0.8,
                          width: context.width * 0.8,
                        ),
                      ),
                    ),
                  );
                })),
          )
        : const SizedBox.shrink();
  }
}
