import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/tasks/providers/tasks_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../models/employee_request_model.dart';

class AddRequestReview extends HookConsumerWidget {
  final EmployeeRequestModel request;

  const AddRequestReview({
    super.key,
    required this.request,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final taskController = ref.watch(taskControllerNotifierProvider(context));

    final emailController = useTextEditingController(text: request.userEmail);

    final reviewController = useTextEditingController();

    final reviewFocusNode = useFocusNode();

    final formKey = useState(GlobalKey<FormState>());

    useEffect(() {
      reviewFocusNode.requestFocus();

      return () {};
    }, []);

    void validateAndAddOrEditTask() async {
      if (formKey.value.currentState!.validate()) {
        await taskController.reviewRequest(
          request: request,
          review: reviewController.text,
        );
      }
    }

    if (taskController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    return Dialog(
        child: Form(
      key: formKey.value,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                child: Icon(
                  Icons.notes,
                  color: Colors.white,
                ),
              ),
              context.mediumGap,
              Text(
                context.tr.addReview,
                style: context.whiteTitle,
              ),
            ],
          ),

          context.largeGap,

          //! Email
          BaseTextField(
            label: context.tr.email,
            enabled: false,
            isWhiteText: true,
            controller: emailController,
            validator: (value) {
              return Validations.email(value,
                  invalidEmailMessage: context.tr.invalidEmail,
                  emptyEmailMessage: context.tr.fieldCannotBeEmpty);
            },
          ),

          context.largeGap,

          //! Review
          BaseTextField(
            label: context.tr.review,
            focusNode: reviewFocusNode,
            isWhiteText: true,
            controller: reviewController,
            maxLines: 3,
            validator: (value) {
              return Validations.mustBeNotEmpty(value,
                  emptyMessage: context.tr.fieldCannotBeEmpty);
            },
          ),

          context.xLargeGap,

          Button(
            label: context.tr.submit,
            onPressed: validateAndAddOrEditTask,
            textColor: Colors.white,
          )
        ],
      ),
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
