import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

class AddUpdateEmployeeFloatingButton extends ConsumerWidget {
  final UserModel? user;

  const AddUpdateEmployeeFloatingButton({
    super.key,
    this.user,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (user != null) {
      return CircleAvatar(
              backgroundColor: ColorManager.successColor,
              child: Icon(
                Icons.edit,
                color: Colors.white,
              ))
          .onTapWithRipple(() => showDialog(
              context: context,
              builder: (_) => AddUpdateUserDialog(
                    user: user,
                  )))
          .paddingOnly(
              left: context.isAppEnglish ? 10 : 0,
              right: context.isAppEnglish ? 0 : 10);
    }
    return FloatingActionButton(
      onPressed: () => showDialog(
          context: context,
          builder: (_) => AddUpdateUserDialog(
                user: user,
              )),
      child: const Icon(Icons.add),
    );
  }
}

class AddUpdateUserDialog extends HookConsumerWidget {
  final UserModel? user;

  const AddUpdateUserDialog({
    super.key,
    this.user,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userController = ref.watch(authControllerNotifierProvider(context));

    final nameController = useTextEditingController(text: user?.name ?? '');
    final emailController = useTextEditingController(text: user?.email ?? '');
    final passwordController =
        useTextEditingController(text: user?.password ?? '');
    final isAdminController = useState(user?.canViewAllTasks ?? false);
    final isActive = useState(user?.isActive ?? true);

    final formKey = useState(GlobalKey<FormState>());

    void validateAndAddUser() async {
      if (formKey.value.currentState!.validate()) {
        final userModel = UserModel(
          uid: user?.uid,
          name: nameController.text,
          email: emailController.text,
          password: passwordController.text,
          canViewAllTasks: isAdminController.value,
          isActive: isActive.value,
        );

        await userController.addUpdateUser(user: userModel);
      }
    }

    if (userController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }
    final isUpdate = user != null;

    return Dialog(
      child: Form(
        key: formKey.value,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const CircleAvatar(
                    backgroundColor: ColorManager.primaryColor,
                    child: Icon(
                      Icons.calendar_today,
                      color: Colors.white,
                    ),
                  ),
                  context.mediumGap,
                  Text(
                    isUpdate ? context.tr.updateUser : context.tr.addNewUser,
                    style: context.whiteTitle,
                  ),
                ],
              ),
              context.largeGap,
              //! Name
              BaseTextField(
                label: context.tr.name,
                controller: nameController,
                isWhiteText: true,
              ),
              context.largeGap,
              //! Email
              BaseTextField(
                label: context.tr.email,
                controller: emailController,
                isWhiteText: true,
              ),
              context.largeGap,
              //! Password
              BaseTextField(
                label: context.tr.password,
                controller: passwordController,
                isWhiteText: true,
                validator: (value) {
                  if (value!.isEmpty) {
                    return context.tr.passwordRequired;
                  }
                  if (value.length < 6) {
                    return context.tr.passwordMinLength;
                  }
                  return null;
                },
              ),
              context.largeGap,
              //! Is Admin
              Row(
                children: [
                  Text(context.tr.isAdmin, style: context.whiteSubTitle),
                  const Spacer(),
                  Switch(
                    value: isAdminController.value,
                    activeColor: ColorManager.primaryColor,
                    onChanged: (value) {
                      isAdminController.value = value;
                    },
                  ),
                ],
              ),
              context.largeGap,
              //! Is Active
              Row(
                children: [
                  Text(context.tr.isActive, style: context.whiteSubTitle),
                  const Spacer(),
                  Switch(
                    value: isActive.value,
                    activeColor: ColorManager.primaryColor,
                    onChanged: (value) {
                      isActive.value = value;
                    },
                  ),
                ],
              ),
              context.largeGap,
              Button(
                label: context.tr.submit,
                onPressed: validateAndAddUser,
                textColor: Colors.white,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
