import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:opti4t_tasks/src/core/consts/firestore_strings.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firestore_service.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/repositories/employee_request_repository.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../../auth/repositories/auth_repository.dart';
import 'request_reports/excel_employee_request_report.dart';
import 'request_reports/pdf_employee_request_report.dart';

final filteredEmployeeRequestRepoProvider =
    Provider<FilteredEmployeeRequestRepository>((ref) {
  final employeeRequestRepo = ref.watch(employeeRequestRepoProvider);
  final authController = ref.watch(authRepoProvider);

  return FilteredEmployeeRequestRepository(
    employeeRequestRepo: employeeRequestRepo,
    authRepo: authController,
  );
});

class FilteredEmployeeRequestRepository with BaseRepository {
  final EmployeeRequestRepository employeeRequestRepo;
  final AuthRepository authRepo;

  FilteredEmployeeRequestRepository(
      {required this.employeeRequestRepo, required this.authRepo});

  //? Review EmployeeRequest & Send to email
  Future<void> reviewEmployeeRequest({
    required EmployeeRequestModel employeeRequest,
    required String review,
  }) async {
    await baseFunction(
      () async {
        final employeeEmail = employeeRequest.userEmail;

        final Email email = Email(
          subject:
              '${employeeRequest.clientName} - ${employeeRequest.date.formatDateToString} Employee Request Review',
          body: review,
          recipients: [employeeEmail],
        );

        await FlutterEmailSender.send(email);
      },
    );
  }

  //? Generate pdf report (selected employee, from date, to date) params then share it to email
  Future<String> generateEmployeeRequestsReport(BuildContext context,
      {required UserModel? employee,
      required String recipientEmail,
      required (DateTime from, DateTime to) selectedDates,
      required List<EmployeeRequestModel> requestsByEmployeeRequest,
      required bool isExcel,
      required String reportType}) async {
    return await baseFunction(() async {
      final fromDate = selectedDates.$1;
      final toDate = selectedDates.$2;

      var generatedPDFReportPath;

      if (isExcel) {
        generatedPDFReportPath = await generateEmployeeRequestsReportExcel(
            employee: employee,
            employeeRequests: requestsByEmployeeRequest,
            fromDate: fromDate,
            toDate: toDate,
            isEnglish: context.isAppEnglish);
      } else {
        var generatedPDF = await generateEmployeeRequestsReportPDF(
          context,
          employeeRequests: requestsByEmployeeRequest,
          employee: employee,
          fromDate: fromDate,
          toDate: toDate,
        );

        generatedPDFReportPath = await savePDF(generatedPDF);
      }

      return generatedPDFReportPath;
    });
  }

  //? Send email
  Future<void> sendEmail({
    required UserModel employee,
    required String recipientEmail,
    required DateTime fromDate,
    required DateTime toDate,
    required String generatedPDFReportPath,
  }) async {
    await baseFunction(
      () async {
        final Email email = Email(
          subject:
              '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString}) Report',
          body: 'Employee: ${employee.name}\n'
              'From: ${fromDate.formatDateToString}\n'
              'To: ${toDate.formatDateToString}\n',
          recipients: [recipientEmail],
          attachmentPaths: [generatedPDFReportPath],
        );

        await FlutterEmailSender.send(email);
      },
    );
  }

  //? save pdf
  Future<String> savePDF(pw.Document pdf, {String title = 'report'}) async {
    log('asfafasf ${pdf}');

    final output = await getTemporaryDirectory();

    final pdfFile = File("${output.path}/$title.pdf");

    await pdfFile.writeAsBytes(await pdf.save());

    return pdfFile.path;
  }

  //? Open pdf
  Future<void> openPDF(String path) async {
    await OpenFile.open(path);
  }

  //? Get EmployeeRequest Data From Same Login Id From Firestore
  Future<List<EmployeeRequestModel>> getAllEmployeeRequests(
      {required DateTime date, DateTime? toDate, String? userId}) async {
    try {
      final employeeRequests =
          await FirestoreService.getData<EmployeeRequestModel>(
              collection: FirebaseStrings.employeeRequests,
              builder: (data) {
                return EmployeeRequestModel.fromJson('', data: data);
              });

      final filteredEmployeeRequestsBetweenDates =
          employeeRequests.where((employeeRequest) {
        return employeeRequest.date!.isAfter(date) &&
            employeeRequest.date!.isBefore(toDate!);
      }).toList();

      filteredEmployeeRequestsBetweenDates
          .sort((a, b) => a.date!.compareTo(b.date!));

      if (userId != null) {
        return filteredEmployeeRequestsBetweenDates
            .where((employeeRequest) => employeeRequest.uid == userId)
            .toList();
      }

      return filteredEmployeeRequestsBetweenDates;
    } catch (e) {
      rethrow;
    }
  }
}
