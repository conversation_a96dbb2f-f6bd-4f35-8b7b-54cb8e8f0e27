import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/screens/clients/controllers/clients_controller.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/repositories/clients_repository.dart';

// * Provider ========================================
final clientControllerProvider =
    Provider.family<ClientRequestController, BuildContext>(
  (ref, context) {
    final clientRepo = ref.watch(clientRepoProvider);

    return ClientRequestController(
      context,
      clientRepo: clientRepo,
    );
  },
);

// * Change Notifier Provider ========================================
final clientControllerNotifierProvider =
    ChangeNotifierProvider.family<ClientRequestController, BuildContext>(
  (ref, context) {
    final clientRepo = ref.watch(clientRepoProvider);

    return ClientRequestController(
      context,
      clientRepo: clientRepo,
    );
  },
);

// * Get Clients Stream Controller ========================================
final getClientsStreamControllerProvider = StreamProvider.family
    .autoDispose<List<ClientRequestModel>, (BuildContext, String?)>(
  (ref, params) {
    final context = params.$1;
    final selectedEmployee = params.$2;

    final clientController = ref.watch(
      clientControllerProvider(context),
    );

    return clientController.getClientsStream(
        selectedEmployeeUid: selectedEmployee);
  },
);

// * Get Projects Stream Controller ========================================
final getProjectStreamControllerProvider = StreamProvider.family
    .autoDispose<List<ClientRequestModel>, (BuildContext, String?)>(
  (ref, params) {
    final context = params.$1;
    final selectedEmployee = params.$2;

    final clientController = ref.watch(
      clientControllerProvider(context),
    );

    return clientController.getProjectsStream(
        selectedEmployeeUid: selectedEmployee);
  },
);

final getClientsFutureProvider = FutureProvider.family
    .autoDispose<List<ClientRequestModel>, (BuildContext, bool)>(
  (ref, params) {
    final clientController = ref.watch(
      clientControllerProvider(params.$1),
    );

    return clientController.getClientsFuture(
      isProject: params.$2,
    );
  },
);

//
