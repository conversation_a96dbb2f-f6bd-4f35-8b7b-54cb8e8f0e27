import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/services/media/controller/media_controller.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/animated/lottie_icon.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/dialog/close_request_dialog.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/floating_buttons/add_project_floating_button.dart';
import 'package:xr_helper/xr_helper.dart';

class ProjectRequestCardActions extends ConsumerWidget {
  final ClientRequestModel request;
  final bool fromFiltered;

  const ProjectRequestCardActions(
      {super.key, required this.request, this.fromFiltered = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final completedRequest = request.isCompleted;

    return Wrap(
      children: [
        //! Check Icon
        CircleAvatar(
                backgroundColor: completedRequest
                    ? ColorManager.primaryColor
                    : ColorManager.secondaryColor,
                child: Icon(
                  completedRequest ? Icons.check : CupertinoIcons.doc_checkmark,
                  color: Colors.white,
                ))
            .onTapWithRipple(() => fromFiltered
                ? null
                : showDialog(
                    context: context,
                    builder: (_) => CloseRequestDialog(
                          request: request,
                          isProject: true,
                        )))
            .paddingSymmetric(
                horizontal: completedRequest ? 0 : AppSpaces.smallPadding),

        //! Edit Icon
        if (!request.isCompleted && !fromFiltered)
          //&& UserModelHelper.isAdmin()
          const CircleAvatar(
                  backgroundColor: ColorManager.successColor,
                  child: BaseLottieIcon('assets/animated/edit.json',
                      width: 20, height: 20))
              .onTapWithRipple(
                  () => showDialog(
                        context: context,
                        builder: (_) => AddProjectDialog(
                          client: request,
                          clientId: request.parentId,
                        ),
                      ).then((value) => mediaController.clearFiles()),
                  radius: 5),
      ],
    );
  }
}
