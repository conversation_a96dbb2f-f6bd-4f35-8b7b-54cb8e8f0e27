import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/extensions/riverpod_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/providers/clients_providers.dart';
import 'package:opti4t_tasks/src/screens/clients/view/projects/projects_list/widgets/projects_list_widget.dart';
import 'package:xr_helper/xr_helper.dart';

class ProjectsList extends ConsumerWidget {
  final ValueNotifier<UserModel?>? selectedEmployee;
  final bool fromHome;

  const ProjectsList({
    super.key,
    this.selectedEmployee,
    this.fromHome = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params = (context, selectedEmployee?.value?.uid);

    final projects = ref.watch(getProjectStreamControllerProvider(params));

    return projects.get(data: (clients) {
      List<ClientRequestModel> filteredClients = clients;

      if (clients.isEmpty) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              maxRadius: 60,
              child: Icon(
                Icons.work_outline,
                color: Colors.white,
                size: 40,
              ),
            ),
            context.largeGap,
            Text(
              context.tr.noProjects,
              style: context.whiteHeadLine,
            ),
          ],
        );
      }

      return ProjectsListWidget(
        clients: filteredClients,
        fromFilter: fromHome,
        // selectedClient: selectedClient,
      );
    });
  }
}
