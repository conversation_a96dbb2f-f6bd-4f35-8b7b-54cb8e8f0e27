import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/expenses/model/expenses_model.dart';

import 'expenses_card/expenses_card_widget.dart';

class ExpensesList extends StatelessWidget {
  final List<ExpensesModel> expenses;

  const ExpensesList({super.key, required this.expenses});

  @override
  Widget build(BuildContext context) {
    if (expenses.isEmpty) {
      return Center(
        child: Text(
          context.tr.noExpenses,
          style: context.headLine,
        ),
      ).paddingOnly(top: context.height / 3.2);
    }
    return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) => ExpensesCardWidget(
              expense: expenses[index],
            ),
        separatorBuilder: (context, index) => context.mediumGap,
        itemCount: expenses.length);
  }
}
