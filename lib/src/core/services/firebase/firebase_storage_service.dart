import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';

class FirebaseStorageService {
  static final _storage = FirebaseStorage.instance;

  static Future<String> uploadFile({
    required String filePath,
    required String fileName,
  }) async {
    final ref = _storage.ref().child(fileName).child(filePath);

    final uploadTask = ref.putFile(
      File(filePath),
    );
    final snapshot = await uploadTask.whenComplete(() => null);
    final url = await snapshot.ref.getDownloadURL();

    return url;
  }

  //? Delete File by url
  static Future<void> deleteFileByUrl({
    required String url,
  }) async {
    final ref = _storage.refFromURL(url);

    await ref.delete();
  }
}
