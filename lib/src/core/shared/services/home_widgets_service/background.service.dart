import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_widget/home_widget.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

/// The name associated with the UI isolate's [SendPort].
const String isolateName = 'prayer_widget_isolate';

/// Configuration constants for alarm management
class AlarmConfig {
  static const int maxConcurrentAlarms =
      2; // Only schedule next prayer + midnight
  static const int nextPrayerAlarmId = 1; // ID for next prayer alarm
  static const int midnightAlarmId = 999999; // ID for midnight refresh alarm
}

/// A port used to communicate from a background isolate to the UI isolate.
ReceivePort port = ReceivePort();

/// Initialize the background service
Future<void> initializeBackgroundService() async {
  if (Platform.isAndroid) {
    // Register the UI isolate's SendPort to allow for communication from the background isolate
    IsolateNameServer.registerPortWithName(
      port.sendPort,
      isolateName,
    );

    // Request exact alarm permission
    await Permission.scheduleExactAlarm.request();

    // Initialize Android Alarm Manager
    await AndroidAlarmManager.initialize();
  }
}

/// The background callback for prayer time updates
@pragma('vm:entry-point')
void prayerTimeUpdateCallback() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorageService.init();
  await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

  Log.i('Background task started at ${DateTime.now()}');

  final calendarController =
      ProviderContainer().read(calendarControllerNotifierProvider);

  CalendarController.calendar.value =
      await calendarController.getCalendarFromLocal();

  final now = DateTime.now();
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;
  var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

  Log.i('Current time: $now');
  Log.i('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

  if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
    final nextDay = (now.hour >= 17 && now.hour <= 23)
        ? DateTime(now.year, now.month, now.day + 1, 0, 0)
        : now;

    Log.i('Fetching next day data for Fajr: $nextDay');
    currentDayData = calendarController.calendarByDate(nextDay);
    prayerTimes = currentDayData.prayerTimes;
    nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
    Log.i(
        'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
  }

  currentDayData = calendarController.calendarByDate(now);
  prayerTimes = currentDayData.prayerTimes;

  await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);

  // Only save iOS widget data on iOS devices
  if (Platform.isIOS) {
    await HomeWidgetService.saveIOSAllPrayerTimesForWidget();
  }

  await HomeWidget.updateWidget(
      iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

  if (Platform.isAndroid) {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget4x1);
  }

  Log.i('Widget updated successfully');

  // Notify UI isolate if available
  final uiSendPort = IsolateNameServer.lookupPortByName(isolateName);
  uiSendPort?.send(null);

  // Schedule next prayer time update using sequential scheduling
  try {
    if (Platform.isAndroid) {
      await scheduleNextPrayerSequentially();
    }
  } catch (e) {
    Log.e('Error scheduling next update: $e');
  }

  Log.i('Background task completed at ${DateTime.now()}');
}

/// Schedule the next prayer time sequentially (only one at a time)
Future<void> scheduleNextPrayerSequentially() async {
  if (Platform.isIOS) return;

  try {
    // Cancel any existing alarms first
    await AndroidAlarmManager.cancel(AlarmConfig.nextPrayerAlarmId);
    await AndroidAlarmManager.cancel(AlarmConfig.midnightAlarmId);

    final calendarController =
        ProviderContainer().read(calendarControllerNotifierProvider);

    final now = DateTime.now();
    final nextPrayerTime =
        _getNextPrayerTimeSequential(calendarController, now);

    if (nextPrayerTime != null) {
      await AndroidAlarmManager.oneShotAt(
        nextPrayerTime['dateTime'],
        AlarmConfig.nextPrayerAlarmId,
        prayerTimeUpdateCallback,
        exact: true,
        wakeup: true,
        rescheduleOnReboot: true,
      );

      Log.i(
          'Scheduled next prayer: ${nextPrayerTime['name']} at ${nextPrayerTime['dateTime']}');
    }

    // Always schedule midnight update for tomorrow
    await _scheduleMidnightUpdate();
  } catch (e) {
    Log.e('Error scheduling next prayer sequentially: $e');
  }
}

/// Get the next prayer time with proper date handling
Map<String, dynamic>? _getNextPrayerTimeSequential(
    CalendarController calendarController, DateTime now) {
  // Try today first
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;

  final prayers = [
    {'name': 'fajr', 'time': prayerTimes.fajr},
    {'name': 'sunrise', 'time': prayerTimes.sunrise},
    {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
    {'name': 'asr', 'time': prayerTimes.asr},
    {'name': 'maghrib', 'time': prayerTimes.maghrib},
    {'name': 'isha', 'time': prayerTimes.isha},
  ];

  // Check for remaining prayers today
  for (final prayer in prayers) {
    final timeString = prayer['time'] as String;
    if (timeString.isEmpty) continue;

    try {
      final timeParts = timeString.split(':');
      if (timeParts.length < 2) continue;

      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);
      final prayerDateTime =
          DateTime(now.year, now.month, now.day, hour, minute);

      if (prayerDateTime.isAfter(now)) {
        return {
          'name': prayer['name'],
          'time': timeString,
          'dateTime': prayerDateTime,
        };
      }
    } catch (e) {
      Log.e('Error parsing prayer time ${prayer['name']}: $e');
    }
  }

  // No prayers left today, get tomorrow's Fajr
  final tomorrow = DateTime(now.year, now.month, now.day + 1);
  final tomorrowData = calendarController.calendarByDate(tomorrow);
  final tomorrowPrayers = tomorrowData.prayerTimes;

  if (tomorrowPrayers.fajr.isNotEmpty) {
    try {
      final timeParts = tomorrowPrayers.fajr.split(':');
      if (timeParts.length >= 2) {
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        final fajrDateTime =
            DateTime(tomorrow.year, tomorrow.month, tomorrow.day, hour, minute);

        return {
          'name': 'fajr',
          'time': tomorrowPrayers.fajr,
          'dateTime': fajrDateTime,
        };
      }
    } catch (e) {
      Log.e('Error parsing tomorrow\'s Fajr time: $e');
    }
  }

  return null;
}

/// Schedule midnight update for tomorrow
Future<void> _scheduleMidnightUpdate() async {
  try {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final midnightUpdate =
        DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 0, 1); // 00:01 AM

    await AndroidAlarmManager.oneShotAt(
      midnightUpdate,
      AlarmConfig.midnightAlarmId,
      prayerTimeUpdateCallback,
      exact: true,
      wakeup: true,
      rescheduleOnReboot: true,
    );

    Log.i('Scheduled midnight update for: $midnightUpdate');
  } catch (e) {
    Log.e('Error scheduling midnight update: $e');
  }
}

/// Schedule prayer updates using sequential approach (replaces old bulk scheduling)
Future<void> scheduleAllFuturePrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Use sequential scheduling instead of bulk scheduling
    await scheduleNextPrayerSequentially();
    Log.i('Sequential prayer scheduling initiated');
  } catch (e) {
    Log.e('Error scheduling future prayer updates: $e');
  }
}

// Removed old bulk scheduling function - replaced with sequential scheduling

/// Schedule daily prayer updates using sequential approach
Future<void> scheduleAllDailyPrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Use sequential scheduling instead of bulk scheduling
    await scheduleNextPrayerSequentially();
    Log.i('Sequential daily prayer scheduling completed');
  } catch (e) {
    Log.e('Error scheduling daily prayer updates: $e');
  }
}

/// Schedule prayer times for a specific day using android_alarm_manager_plus
Future<void> _schedulePrayerTimesForDate(CalendarController calendarController,
    DateTime date, String dayLabel) async {
  try {
    final dayData = calendarController.calendarByDate(date);
    final prayerTimes = dayData.prayerTimes;
    final now = DateTime.now();

    // List of prayer times to schedule
    final prayers = [
      {'name': 'fajr', 'time': prayerTimes.fajr},
      {'name': 'sunrise', 'time': prayerTimes.sunrise},
      {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
      {'name': 'asr', 'time': prayerTimes.asr},
      {'name': 'maghrib', 'time': prayerTimes.maghrib},
      {'name': 'isha', 'time': prayerTimes.isha},
    ];

    for (final prayer in prayers) {
      try {
        final timeString = prayer['time'] as String;
        if (timeString.isEmpty) continue;

        final timeParts = timeString.split(':');
        if (timeParts.length < 2) continue;

        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        final prayerDateTime =
            DateTime(date.year, date.month, date.day, hour, minute);

        // Only schedule if the prayer time is in the future
        if (prayerDateTime.isAfter(now)) {
          final alarmId = _createAlarmId(date, prayers.indexOf(prayer));
          await _scheduleAlarmAt(
              alarmId, prayerDateTime, prayerTimeUpdateCallback);
          Log.i(
              'Scheduled ${prayer['name']} for $dayLabel at ${prayer['time']}');
        }
      } catch (e) {
        Log.e(
            'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
      }
    }
  } catch (e) {
    Log.e('Error scheduling prayer times for $dayLabel: $e');
  }
}

/// Schedule prayer times for a future day (used by scheduleAllFuturePrayerUpdates)
// Future<int> _schedulePrayerTimesForFutureDate(
//     DayModel day, DateTime dayDate) async {
//   int alarmsScheduled = 0;
//
//   try {
//     final prayerTimes = day.prayerTimes;
//     final now = DateTime.now();
//
//     // List of prayer times to schedule
//     final prayers = [
//       {'name': 'fajr', 'time': prayerTimes.fajr},
//       {'name': 'sunrise', 'time': prayerTimes.sunrise},
//       {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
//       {'name': 'asr', 'time': prayerTimes.asr},
//       {'name': 'maghrib', 'time': prayerTimes.maghrib},
//       {'name': 'isha', 'time': prayerTimes.isha},
//     ];
//
//     for (final prayer in prayers) {
//       try {
//         final timeString = prayer['time'] as String;
//         if (timeString.isEmpty) continue;
//
//         final timeParts = timeString.split(':');
//         if (timeParts.length < 2) continue;
//
//         final hour = int.parse(timeParts[0]);
//         final minute = int.parse(timeParts[1]);
//         final prayerDateTime =
//             DateTime(dayDate.year, dayDate.month, dayDate.day, hour, minute);
//
//         // Only schedule if the prayer time is in the future
//         if (prayerDateTime.isAfter(now)) {
//           final alarmId = _createAlarmId(dayDate, prayers.indexOf(prayer));
//           await _scheduleAlarmAt(
//               alarmId, prayerDateTime, prayerTimeUpdateCallback);
//           alarmsScheduled++;
//           Log.i(
//               'Scheduled ${prayer['name']} for ${dayDate.toString().split(' ')[0]} at ${prayer['time']}');
//         }
//       } catch (e) {
//         Log.e(
//             'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
//       }
//     }
//   } catch (e) {
//     Log.e('Error scheduling prayer times for future date: $e');
//   }
//
//   return alarmsScheduled;
// }

/// Schedule an alarm at a specific time using android_alarm_manager_plus
Future<void> _scheduleAlarmAt(
    int alarmId, DateTime scheduledTime, Function callback) async {
  try {
    await AndroidAlarmManager.oneShotAt(
      scheduledTime,
      alarmId,
      callback,
      exact: true,
      wakeup: true,
      alarmClock: true,
      rescheduleOnReboot: true,
    );
    Log.i('Scheduled alarm $alarmId for ${scheduledTime.toString()}');
  } catch (e) {
    Log.e('Error_scheduling alarm $alarmId: $e');

    // If we hit the alarm limit, throw a more specific error
    if (e.toString().contains('Maximum limit of concurrent alarms')) {
      throw Exception('Alarm limit reached: ${e.toString()}');
    }
    rethrow;
  }
}

/// Create a unique alarm ID based on date and prayer index
int _createAlarmId(DateTime date, int prayerIndex) {
  // Create unique ID: YYYYMMDDHH where HH is prayer index (0-5)
  final year = date.year % 100; // Last 2 digits of year
  final month = date.month;
  final day = date.day;

  // Format: YYMMDDHH where HH is prayer index
  return (year * 1000000) + (month * 10000) + (day * 100) + prayerIndex;
}

/// Cancel all scheduled alarms (simplified for sequential scheduling)
Future<void> cancelAllScheduledAlarms() async {
  if (Platform.isAndroid) {
    try {
      // Cancel the two alarms we use in sequential scheduling
      await AndroidAlarmManager.cancel(AlarmConfig.nextPrayerAlarmId);
      await AndroidAlarmManager.cancel(AlarmConfig.midnightAlarmId);

      // Also cancel any old alarms that might exist from previous versions
      for (int i = 1; i <= 10; i++) {
        await AndroidAlarmManager.cancel(i);
      }
      await AndroidAlarmManager.cancel(999999); // Old midnight alarm ID

      Log.i('All scheduled alarms cancelled (sequential scheduling)');
    } catch (e) {
      Log.e('Error cancelling alarms: $e');
    }
  }
}

// Removed old helper functions - no longer needed with sequential scheduling
