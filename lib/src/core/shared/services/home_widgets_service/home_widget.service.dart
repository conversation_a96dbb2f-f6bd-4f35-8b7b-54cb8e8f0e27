import 'dart:convert';
import 'dart:io';

import 'package:background_fetch/background_fetch.dart' hide NetworkType;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/extensions/string_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/calendar/controllers/calendar.controller.dart';
import '../../../../features/calendar/models/calendar_model.dart';
import '../../../../features/calendar/providers/calendar.providers.dart';
import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';
import 'background.service.dart' as bg;

class HomeWidgetService {
  static Future<void> initialize() async {
    await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

    try {
      if (Platform.isAndroid) {
        // Configure BackgroundFetch with longer interval - actual scheduling will be done by prayer times
        BackgroundFetch.configure(
          BackgroundFetchConfig(
            minimumFetchInterval:
                2, // 1 hour fallback, but we'll use specific prayer time scheduling
            stopOnTerminate: false,
            enableHeadless: true,
            startOnBoot: true,
            forceAlarmManager: true,
          ),
          (taskId) => bg.backgroundFetchHeadlessTask(taskId.toString()),
        );
        Log.i(
            'Android: BackgroundFetch initialized with prayer time scheduling');
      } else {
        // For iOS, we'll rely on the widget's own timeline updates
        Log.i('iOS: Using widget timeline for updates');
      }

      await Permission.ignoreBatteryOptimizations.request();
    } catch (e) {
      Log.e('Background_Permission_Error: $e');
    }
  }

  static Future<void> update(BuildContext context,
      {required WidgetRef ref}) async {
    try {
      Log.w('INIT');
      final calendarController = ref.watch(calendarControllerNotifierProvider);
      final now = DateTime.now();

      // Get current day's data
      var currentDayData = calendarController.calendarByDate(now);
      var prayerTimes = currentDayData.prayerTimes;

      //! Determine the next prayer time
      var nextPrayerTime = getNextPrayerTime(prayerTimes);

      Log.w('Current time: $now');
      Log.w('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

      //! If next prayer is Fajr and it's still before midnight but after Isha (e.g., between 19:00 and 23:59)
      if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
        final nextDay = (now.hour >= 17 && now.hour <= 23)
            ? DateTime(now.year, now.month, now.day + 1, 0, 0)
            : now;

        Log.w('Fetching next day data for Fajr: $nextDay');

        currentDayData = calendarController.calendarByDate(nextDay);

        prayerTimes = currentDayData.prayerTimes;

        nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

        Log.i(
            'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
      }

      currentDayData = calendarController.calendarByDate(now);
      prayerTimes = currentDayData.prayerTimes;

      await savePrayerTimes(prayerTimes, nextPrayerTime);

      _updateWidgets();

      // Schedule prayer time updates for Android
      if (Platform.isAndroid) {
        await scheduleAllPrayerTimeUpdates();
      }
    } catch (e, s) {
      Log.e('Widget_ERROR: $e $s');
    }
  }

  static Future<void> savePrayerTimes(
      PrayerTimeModel prayerTimes, NextPrayerTime nextPrayerTime) async {
    await HomeWidget.saveWidgetData('fajr', prayerTimes.fajr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'sunrise', prayerTimes.sunrise.convertTo12Hour);
    await HomeWidget.saveWidgetData('dhuhr', prayerTimes.dhuhr.convertTo12Hour);
    await HomeWidget.saveWidgetData('asr', prayerTimes.asr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'maghrib', prayerTimes.maghrib.convertTo12Hour);
    await HomeWidget.saveWidgetData('isha', prayerTimes.isha.convertTo12Hour);
    await HomeWidget.saveWidgetData('nextPrayer',
        '${nextPrayerTime.name}\n${nextPrayerTime.time.formatTime}');
    await HomeWidget.saveWidgetData('titleText',
        nextPrayerTime.name == "الشروق" ? "موعد الشروق" : "الصلاة القادمة");
  }

  static Future<void> saveIOSAllPrayerTimesForWidget() async {
    try {
      final calendarData = CalendarController.calendarBySummerTime;

      if (calendarData.isEmpty) {
        Log.w('No calendar data available for widget');
        return;
      }

      // Create a map of all prayer times for the next 30 days
      final Map<String, Map<String, String>> allPrayerTimes = {};

      for (final calendar in calendarData) {
        for (final day in calendar.days) {
          if (day.gregorianDate.isNotEmpty && day.prayerTimes.fajr.isNotEmpty) {
            allPrayerTimes[day.gregorianDate] = {
              'fajr': day.prayerTimes.fajr.convertTo12Hour,
              'dhuhr': day.prayerTimes.dhuhr.convertTo12Hour,
              'asr': day.prayerTimes.asr.convertTo12Hour,
              'maghrib': day.prayerTimes.maghrib.convertTo12Hour,
              'isha': day.prayerTimes.isha.convertTo12Hour,
              'sunrise': day.prayerTimes.sunrise.convertTo12Hour,
            };
          }
        }
      }

      // Save all prayer times as JSON string
      await HomeWidget.saveWidgetData(
          'allPrayerTimes', jsonEncode(allPrayerTimes));

      // Also save current day's prayer times for immediate use with next day Fajr logic
      final now = DateTime.now();
      final calendarController =
          ProviderContainer().read(calendarControllerNotifierProvider);

      var currentDayData = calendarController.calendarByDate(now);
      var prayerTimes = currentDayData.prayerTimes;
      var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

      // Handle next day Fajr logic for iOS (same as Android)
      if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
        final nextDay = (now.hour >= 17 && now.hour <= 23)
            ? DateTime(now.year, now.month, now.day + 1, 0, 0)
            : now;

        Log.w('iOS: Fetching next day data for Fajr: $nextDay');

        currentDayData = calendarController.calendarByDate(nextDay);
        prayerTimes = currentDayData.prayerTimes;
        nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

        Log.i(
            'iOS: Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
      }

      await savePrayerTimes(prayerTimes, nextPrayerTime);

      Log.i('Saved ${allPrayerTimes.length} days of prayer times for widget');
    } catch (e, s) {
      Log.e('Error saving all prayer times for widget: $e\n$s');
    }
  }

  static void _updateWidgets() async {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

    if (Platform.isAndroid) {
      await HomeWidget.updateWidget(
          iOSName: AppConsts.iosWidget,
          androidName: AppConsts.androidWidget4x1);
    }
  }

  /// Schedule background tasks for all prayer times + midnight
  /// Similar to notification scheduling but for widget updates
  static Future<void> scheduleAllPrayerTimeUpdates() async {
    if (!Platform.isAndroid) return;

    try {
      // Cancel any existing scheduled tasks first
      await BackgroundFetch.stop();

      final futureDays = CalendarController.calendarBySummerTime
          .expand((calendar) => calendar.days);

      final now = DateTime.now();
      final updateTimes = <DateTime>[];

      // Collect all prayer times + midnight for future days
      for (final day in futureDays) {
        if (day.gregorianDate.isEmpty || day.prayerTimes.fajr.isEmpty) continue;

        final dayDate = DateTime(
          day.gregorianYearNumber.toInt.toInt(),
          day.gregorianMonthNumber.toInt.toInt(),
          day.gregorianDayNumber.toInt(),
        );

        // Skip past days
        if (dayDate.isBefore(DateTime(now.year, now.month, now.day))) continue;

        // Add midnight update for tomorrow's prayer times
        final midnightUpdate = DateTime(
          dayDate.year,
          dayDate.month,
          dayDate.day,
          0,
          0,
        );
        if (midnightUpdate.isAfter(now)) {
          updateTimes.add(midnightUpdate);
        }

        // Add all prayer times for this day
        final prayerTimes = [
          day.prayerTimes.fajr,
          day.prayerTimes.sunrise,
          day.prayerTimes.dhuhr,
          day.prayerTimes.asr,
          day.prayerTimes.maghrib,
          day.prayerTimes.isha,
        ];

        for (final prayerTimeStr in prayerTimes) {
          if (prayerTimeStr.isEmpty || prayerTimeStr == '--') continue;

          final timeParts = prayerTimeStr.split(':');
          if (timeParts.length < 2) continue;

          final prayerDateTime = DateTime(
            dayDate.year,
            dayDate.month,
            dayDate.day,
            int.parse(timeParts[0]),
            int.parse(timeParts[1]),
          );

          // Only add future prayer times
          if (prayerDateTime.isAfter(now)) {
            updateTimes.add(prayerDateTime);
          }
        }
      }

      // Sort by date (schedule nearest first)
      updateTimes.sort((a, b) => a.compareTo(b));

      // Schedule the next prayer time update (only the immediate next one)
      if (updateTimes.isNotEmpty) {
        final nextUpdateTime = updateTimes.first;
        final delay = nextUpdateTime.difference(now).inMinutes;

        if (delay > 0) {
          await BackgroundFetch.scheduleTask(
            TaskConfig(
              taskId: "com.transistorsoft.prayer_update",
              delay: delay * 60 * 1000, // Convert to milliseconds
              periodic: false,
              forceAlarmManager: true,
              stopOnTerminate: false,
              startOnBoot: true,
              requiresBatteryNotLow: false,
              requiresCharging: false,
              requiresDeviceIdle: false,
              requiresStorageNotLow: false,
              enableHeadless: true,
            ),
          );

          Log.i(
              'Scheduled_Next widget update for: ${nextUpdateTime.toString()} (in $delay minutes)');
        }
      }
    } catch (e) {
      Log.e('Error scheduling prayer time updates: $e');
    }
  }
}
