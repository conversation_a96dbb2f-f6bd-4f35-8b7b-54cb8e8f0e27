import 'package:admin_dubai/src/features/views/developers/add_edit_developer_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/auth_blok.dart';
import '../../features/bloc/other_settings_bloc.dart';
import '../../features/models/developer_model.dart';
import '../../features/response/developer_response.dart';
import '../utils/resources.dart';

class DeveloperSearchSheet extends HookWidget {
  final DeveloperModel? selectedValue;
  final String? label;
  final void Function(DeveloperModel?)? onChanged;
  final bool isRequired;

  const DeveloperSearchSheet({
    super.key,
    required this.onChanged,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return GestureDetector(
      onTap: () {
        isDropdownOpen.value = true;
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => Container(
            height: MediaQuery.of(context).size.height * 0.75,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: _buildBottomSheetContent(context, isDropdownOpen),
          ),
        ).then((_) => isDropdownOpen.value = false);
      },
      child: Container(
        height: 50,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey),
          color: Colors.white,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                selectedValue != null
                    ? (AuthBloc.isEnglish
                        ? (selectedValue!.nameEn ?? selectedValue!.name ?? '')
                        : (selectedValue!.nameAr ?? selectedValue!.name ?? ''))
                    : (label ?? S.of(context).selectDeveloper),
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
              const Icon(Icons.keyboard_arrow_down, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomSheetContent(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    return HookBuilder(builder: (context) {
      final searchController = useTextEditingController();
      final filteredData = useState<List<DeveloperModel>>([]);
      final allData = useState<List<DeveloperModel>>([]);
      final isLoading = useState(true);

      // Load data
      useEffect(() {
        _loadDevelopers(allData, filteredData, isLoading);
        return null;
      }, []);

      // Search functionality
      useEffect(() {
        searchController.addListener(() {
          final query = searchController.text.toLowerCase();
          filteredData.value = allData.value.where((item) {
            final name = AuthBloc.isEnglish
                ? (item.nameEn ?? item.name ?? '')
                : (item.nameAr ?? item.name ?? '');
            return name.toLowerCase().contains(query);
          }).toList();
        });
        return null;
      }, [searchController]);

      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: searchController,
              style: const TextStyle(color: Colors.black),
              decoration: InputDecoration(
                hintText: S.of(context).searchDevelopers,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const AddEditDeveloperPage()),
                  );
                  _loadDevelopers(allData, filteredData, isLoading);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: GlobalColors.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  S.of(context).addNewDeveloper,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
          ),
          Expanded(
            child: isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : filteredData.value.isEmpty
                    ? Center(child: Text(S.of(context).Therearenoitems))
                    : ListView.builder(
                        itemCount: filteredData.value.length,
                        itemBuilder: (context, index) {
                          final item = filteredData.value[index];
                          final isSelected = selectedValue?.id == item.id;
                          final displayName = AuthBloc.isEnglish
                              ? (item.nameEn ?? item.name ?? '')
                              : (item.nameAr ?? item.name ?? '');

                          return ListTile(
                            selected: isSelected,
                            title: Text(displayName),
                            subtitle: item.description != null
                                ? Text(
                                    AuthBloc.isEnglish
                                        ? (item.descriptionEn ??
                                            item.description ??
                                            '')
                                        : (item.descriptionAr ??
                                            item.description ??
                                            ''),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  )
                                : null,
                            onTap: () {
                              onChanged?.call(item);
                              Navigator.pop(context);
                            },
                            trailing: isSelected
                                ? const Icon(Icons.check, color: Colors.green)
                                : null,
                          );
                        },
                      ),
          ),
        ],
      );
    });
  }

  void _loadDevelopers(
    ValueNotifier<List<DeveloperModel>> allData,
    ValueNotifier<List<DeveloperModel>> filteredData,
    ValueNotifier<bool> isLoading,
  ) async {
    isLoading.value = true;
    try {
      // Trigger the bloc to fetch developers
      othersettingsbloc.getDevelopers(1, 100, '');

      // Listen to the stream for the response
      othersettingsbloc.developers.stream.listen((DeveloperResponse? response) {
        if (response != null && response.code == 1) {
          allData.value = response.results;
          filteredData.value = response.results;
        }
        isLoading.value = false;
      });
    } catch (e) {
      print('Error loading developers: $e');
      isLoading.value = false;
    }
  }
}
