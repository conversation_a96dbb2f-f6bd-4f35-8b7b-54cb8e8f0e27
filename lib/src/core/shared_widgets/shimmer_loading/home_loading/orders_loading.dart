import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

import '../../../resources/app_radius.dart';
import '../main_shimmer_loading.dart';

class OrdersLoading extends StatelessWidget {
  const OrdersLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: NeverScrollableScrollPhysics(),
      itemCount: 8,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return _OrdersLoadingCard();
      },
    );
  }
}

class _OrdersLoadingCard extends StatelessWidget {
  const _OrdersLoadingCard();

  @override
  Widget build(BuildContext context) {
    return MainShimmerLoading(
      child: Container(
        margin: EdgeInsets.only(
          bottom: AppSpaces.mediumPadding,
          right: AppSpaces.mediumPadding,
          left: AppSpaces.mediumPadding,
        ),
        height: 150,
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        width: double.infinity,
        decoration: BoxDecoration(
          color: ColorManager.shimmerBaseColor,
          borderRadius: BorderRadius.circular(AppRadius.imageContainerRadius),
        ),
      ),
    );
  }
}
