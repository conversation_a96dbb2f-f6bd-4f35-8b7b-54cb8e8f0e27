import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import 'color_manager.dart';

class AppTheme {
  final TextTheme appTextTheme;

  const AppTheme({required this.appTextTheme});

  //? Out Line Border -------------------------------------
  OutlineInputBorder get _outLineBorder => OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        borderSide: const BorderSide(color: ColorManager.fieldColor),
      );

  //? None Input Border -------------------------------------
  InputDecorationTheme get noneInputBorder => const InputDecorationTheme(
      enabledBorder:
          OutlineInputBorder(borderSide: BorderSide(color: ColorManager.black)),
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none);

  //? App Bar Theme -------------------------------------
  get _appBarTheme => const AppBarTheme(
        titleTextStyle: TextStyle(
          color: ColorManager.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        backgroundColor: ColorManager.primaryColor,
        iconTheme: IconThemeData(color: Colors.white),
      );

  get colorScheme => const ColorScheme.light().copyWith(
      primary: ColorManager.primaryColor,
      secondary: ColorManager.secondaryColor);

  get darkColorScheme => const ColorScheme.dark().copyWith(
      primary: ColorManager.primaryColor,
      secondary: ColorManager.secondaryColor);

  //? Button Theme -------------------------------------
  get _buttonTheme => ElevatedButtonThemeData(
      style: ButtonStyle(
          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.radius12))),
          foregroundColor: const WidgetStatePropertyAll(Colors.white70),
          backgroundColor:
              const WidgetStatePropertyAll(ColorManager.primaryColor)));

  //? Input Decoration Theme -------------------------------------
  get _inputDecorationTheme => InputDecorationTheme(
        prefixIconColor: ColorManager.iconColor,
        suffixIconColor: ColorManager.iconColor,
        iconColor: ColorManager.iconColor,
        border: _outLineBorder,
        enabledBorder: _outLineBorder,
        focusedBorder: _outLineBorder,
        errorBorder: _outLineBorder.copyWith(
            borderSide: const BorderSide(color: ColorManager.errorColor)),
        fillColor: ColorManager.fieldColor.withOpacity(0.3),
      );

  //? Bottom Navigation Bar Theme -------------------------------------
  get _bottomNavigationBar => const BottomNavigationBarThemeData(
        elevation: 20,
        showSelectedLabels: true,
        backgroundColor: ColorManager.primaryColor,
        selectedIconTheme: IconThemeData(color: ColorManager.white),
        unselectedIconTheme: IconThemeData(color: ColorManager.white),
        selectedItemColor: ColorManager.white,
        unselectedItemColor: ColorManager.iconColor,
      );

  //? List Tile Theme
  final _listTile = const ListTileThemeData(
    iconColor: ColorManager.white,
  );

  //? Floating Action Button Theme
  final _floatingActionButtonThemeData = const FloatingActionButtonThemeData(
    backgroundColor: ColorManager.primaryColor,
    foregroundColor: ColorManager.white,
  );

  //? Icon Button Theme
  final _iconButtonThemeData = const IconButtonThemeData(
      style:
          ButtonStyle(iconColor: MaterialStatePropertyAll(ColorManager.black)));

  //? Expansion Tile Theme
  final _expansionTileTheme = const ExpansionTileThemeData(
    iconColor: ColorManager.black,
    collapsedBackgroundColor: ColorManager.white,
    shape: RoundedRectangleBorder(
        side: BorderSide.none,
        borderRadius: BorderRadius.all(Radius.circular(10))),
    collapsedShape: RoundedRectangleBorder(
        side: BorderSide.none,
        borderRadius: BorderRadius.all(Radius.circular(10))),
  );

  //? Theme Data -------------------------------------
  ThemeData appTheme() => ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: ColorManager.secondaryColor,
      // textTheme: AppConsts.fontFamily,
      textTheme: appTextTheme,
      expansionTileTheme: _expansionTileTheme,

      //! AppBar Theme
      appBarTheme: _appBarTheme,
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.all(ColorManager.white),
        checkColor: WidgetStateProperty.all(ColorManager.primaryColor),
        side: const BorderSide(color: ColorManager.primaryColor),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(2),
            side: const BorderSide(color: ColorManager.primaryColor, width: 2)),
      ),

      //! Color Scheme
      colorScheme: colorScheme,
      iconButtonTheme: _iconButtonThemeData,

      //! Button Theme
      elevatedButtonTheme: _buttonTheme,

      //! input decoration theme
      inputDecorationTheme: _inputDecorationTheme,

      //! Bottom Navigation Bar Theme
      bottomNavigationBarTheme: _bottomNavigationBar,

      //! Floating Action Button Theme
      floatingActionButtonTheme: _floatingActionButtonThemeData,

      //! List Tile Theme
      listTileTheme: _listTile);

  //? Dark Theme Data -------------------------------------
  ThemeData appDarkTheme() => ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: ColorManager.secondaryColor,
      textTheme: appTextTheme,
      expansionTileTheme: _expansionTileTheme,

      //! AppBar Theme
      appBarTheme: _appBarTheme.copyWith(
        backgroundColor: Colors.grey[900],
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.all(ColorManager.primaryColor),
        checkColor: WidgetStateProperty.all(ColorManager.white),
        side: const BorderSide(color: ColorManager.primaryColor),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(2),
            side: const BorderSide(color: ColorManager.primaryColor, width: 2)),
      ),

      //! Color Scheme
      colorScheme: darkColorScheme,
      iconButtonTheme: const IconButtonThemeData(
          style: ButtonStyle(iconColor: WidgetStatePropertyAll(Colors.white))),

      //! Button Theme
      elevatedButtonTheme: _buttonTheme,

      //! input decoration theme
      inputDecorationTheme: _inputDecorationTheme,

      //! Bottom Navigation Bar Theme
      bottomNavigationBarTheme: _bottomNavigationBar,

      //! Floating Action Button Theme
      floatingActionButtonTheme: _floatingActionButtonThemeData,

      //! List Tile Theme
      listTileTheme: _listTile);
}
