// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Login`
  String get Login {
    return Intl.message('Login', name: 'Login', desc: '', args: []);
  }

  /// `Email Adress`
  String get EmailAdress {
    return Intl.message(
      'Email Adress',
      name: 'EmailAdress',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get Password {
    return Intl.message('Password', name: 'Password', desc: '', args: []);
  }

  /// `Forgot Password`
  String get ForgotPassword {
    return Intl.message(
      'Forgot Password',
      name: 'ForgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your registered email in order to send you a 4-digit OTP to reset your password`
  String get enteremail {
    return Intl.message(
      'Please enter your registered email in order to send you a 4-digit OTP to reset your password',
      name: 'enteremail',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get Email {
    return Intl.message('Email', name: 'Email', desc: '', args: []);
  }

  /// `Send 4-digits OTP`
  String get SenddigitsOTP {
    return Intl.message(
      'Send 4-digits OTP',
      name: 'SenddigitsOTP',
      desc: '',
      args: [],
    );
  }

  /// `Enter 4-digits OTP`
  String get EnterdigitsOTP {
    return Intl.message(
      'Enter 4-digits OTP',
      name: 'EnterdigitsOTP',
      desc: '',
      args: [],
    );
  }

  /// `4-digit OTP Was sent to your email,Please check and enter it to continue`
  String get checkemail {
    return Intl.message(
      '4-digit OTP Was sent to your email,Please check and enter it to continue',
      name: 'checkemail',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get Continue {
    return Intl.message('Continue', name: 'Continue', desc: '', args: []);
  }

  /// `Enter a New Password,that is easy to remember`
  String get EnteraNewPassword {
    return Intl.message(
      'Enter a New Password,that is easy to remember',
      name: 'EnteraNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `Enter New Password`
  String get EnterNewPassword {
    return Intl.message(
      'Enter New Password',
      name: 'EnterNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `Confirm New Password`
  String get ConfirmNewPassword {
    return Intl.message(
      'Confirm New Password',
      name: 'ConfirmNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `Reset password`
  String get Resetpassword {
    return Intl.message(
      'Reset password',
      name: 'Resetpassword',
      desc: '',
      args: [],
    );
  }

  /// `Type`
  String get type {
    return Intl.message('Type', name: 'type', desc: '', args: []);
  }

  /// `password changed successfullly`
  String get passwordchangedsuccessfullly {
    return Intl.message(
      'password changed successfullly',
      name: 'passwordchangedsuccessfullly',
      desc: '',
      args: [],
    );
  }

  /// `please use new password to login`
  String get pleaseusenewpasswordtologin {
    return Intl.message(
      'please use new password to login',
      name: 'pleaseusenewpasswordtologin',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to Dashboard`
  String get WelcometoDashboard {
    return Intl.message(
      'Welcome to Dashboard',
      name: 'WelcometoDashboard',
      desc: '',
      args: [],
    );
  }

  /// `Manage all features from one place`
  String get Manageallfeaturesfromoneplace {
    return Intl.message(
      'Manage all features from one place',
      name: 'Manageallfeaturesfromoneplace',
      desc: '',
      args: [],
    );
  }

  /// `Categories`
  String get Categories {
    return Intl.message('Categories', name: 'Categories', desc: '', args: []);
  }

  /// `Fetured Videos`
  String get FeturedVideos {
    return Intl.message(
      'Fetured Videos',
      name: 'FeturedVideos',
      desc: '',
      args: [],
    );
  }

  /// `Website`
  String get website {
    return Intl.message('Website', name: 'website', desc: '', args: []);
  }

  /// `Other settigs`
  String get Othersettigs {
    return Intl.message(
      'Other settigs',
      name: 'Othersettigs',
      desc: '',
      args: [],
    );
  }

  /// `Manage Features,types,brands and other stuff`
  String get ManageFeatures {
    return Intl.message(
      'Manage Features,types,brands and other stuff',
      name: 'ManageFeatures',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get Language {
    return Intl.message('Language', name: 'Language', desc: '', args: []);
  }

  /// `Price Per Night`
  String get pricepernight {
    return Intl.message(
      'Price Per Night',
      name: 'pricepernight',
      desc: '',
      args: [],
    );
  }

  /// `Turned On`
  String get TurnedOn {
    return Intl.message('Turned On', name: 'TurnedOn', desc: '', args: []);
  }

  /// `Type`
  String get Type {
    return Intl.message('Type', name: 'Type', desc: '', args: []);
  }

  /// `Logout`
  String get Logout {
    return Intl.message('Logout', name: 'Logout', desc: '', args: []);
  }

  /// `English`
  String get English {
    return Intl.message('English', name: 'English', desc: '', args: []);
  }

  /// `Arabic`
  String get Arabic {
    return Intl.message('Arabic', name: 'Arabic', desc: '', args: []);
  }

  /// `Agents`
  String get Agents {
    return Intl.message('Agents', name: 'Agents', desc: '', args: []);
  }

  /// `Starting`
  String get starting {
    return Intl.message('Starting', name: 'starting', desc: '', args: []);
  }

  /// `All Agents`
  String get AllAgents {
    return Intl.message('All Agents', name: 'AllAgents', desc: '', args: []);
  }

  /// `Add New agent`
  String get AddNewagent {
    return Intl.message(
      'Add New agent',
      name: 'AddNewagent',
      desc: '',
      args: [],
    );
  }

  /// `Holiday Homes Agents`
  String get HolidayHomesAgents {
    return Intl.message(
      'Holiday Homes Agents',
      name: 'HolidayHomesAgents',
      desc: '',
      args: [],
    );
  }

  /// `Car rental Agets`
  String get CarrentalAgets {
    return Intl.message(
      'Car rental Agets',
      name: 'CarrentalAgets',
      desc: '',
      args: [],
    );
  }

  /// `Apply Filter`
  String get ApplyFilter {
    return Intl.message(
      'Apply Filter',
      name: 'ApplyFilter',
      desc: '',
      args: [],
    );
  }

  /// `reset`
  String get reset {
    return Intl.message('reset', name: 'reset', desc: '', args: []);
  }

  /// `Agent company name`
  String get Agentcompanyname {
    return Intl.message(
      'Agent company name',
      name: 'Agentcompanyname',
      desc: '',
      args: [],
    );
  }

  /// `Contact finish date`
  String get Contactfinishdate {
    return Intl.message(
      'Contact finish date',
      name: 'Contactfinishdate',
      desc: '',
      args: [],
    );
  }

  /// `User Information`
  String get UserInformation {
    return Intl.message(
      'User Information',
      name: 'UserInformation',
      desc: '',
      args: [],
    );
  }

  /// `Country`
  String get Country {
    return Intl.message('Country', name: 'Country', desc: '', args: []);
  }

  /// `Full name English`
  String get Fullname {
    return Intl.message(
      'Full name English',
      name: 'Fullname',
      desc: '',
      args: [],
    );
  }

  /// `Email Address`
  String get EmailAddress {
    return Intl.message(
      'Email Address',
      name: 'EmailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Agent Details`
  String get AgentDetails {
    return Intl.message(
      'Agent Details',
      name: 'AgentDetails',
      desc: '',
      args: [],
    );
  }

  /// `Whatsapp`
  String get whatsapp {
    return Intl.message('Whatsapp', name: 'whatsapp', desc: '', args: []);
  }

  /// `Main Agent Information`
  String get MainAgentInformation {
    return Intl.message(
      'Main Agent Information',
      name: 'MainAgentInformation',
      desc: '',
      args: [],
    );
  }

  /// `Agent Company Name`
  String get AgentCompanyName {
    return Intl.message(
      'Agent Company Name',
      name: 'AgentCompanyName',
      desc: '',
      args: [],
    );
  }

  /// `Agenty`
  String get Agenty {
    return Intl.message('Agenty', name: 'Agenty', desc: '', args: []);
  }

  /// `Add Agent`
  String get AddAgent {
    return Intl.message('Add Agent', name: 'AddAgent', desc: '', args: []);
  }

  /// `Edit`
  String get Edit {
    return Intl.message('Edit', name: 'Edit', desc: '', args: []);
  }

  /// `Delete Agent`
  String get DeleteAgent {
    return Intl.message(
      'Delete Agent',
      name: 'DeleteAgent',
      desc: '',
      args: [],
    );
  }

  /// `Are yyou sure you want to delete this agent`
  String get Areyyousureyouwanttodeletethisagent {
    return Intl.message(
      'Are yyou sure you want to delete this agent',
      name: 'Areyyousureyouwanttodeletethisagent',
      desc: '',
      args: [],
    );
  }

  /// `Yes,Delete Agent`
  String get yDeleteAgent {
    return Intl.message(
      'Yes,Delete Agent',
      name: 'yDeleteAgent',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get Notifications {
    return Intl.message(
      'Notifications',
      name: 'Notifications',
      desc: '',
      args: [],
    );
  }

  /// `Request for car rent approved`
  String get Requestforcarrentapproved {
    return Intl.message(
      'Request for car rent approved',
      name: 'Requestforcarrentapproved',
      desc: '',
      args: [],
    );
  }

  /// `Your request for Hyundai Sonata No. 380282 is approved`
  String get YourrequestforHyundaiSonataNo {
    return Intl.message(
      'Your request for Hyundai Sonata No. 380282 is approved',
      name: 'YourrequestforHyundaiSonataNo',
      desc: '',
      args: [],
    );
  }

  /// `Featured Videos`
  String get FeaturedVideos {
    return Intl.message(
      'Featured Videos',
      name: 'FeaturedVideos',
      desc: '',
      args: [],
    );
  }

  /// `All Featured Videos`
  String get AllFeaturedVideos {
    return Intl.message(
      'All Featured Videos',
      name: 'AllFeaturedVideos',
      desc: '',
      args: [],
    );
  }

  /// `Filteredas`
  String get Filteredas {
    return Intl.message('Filteredas', name: 'Filteredas', desc: '', args: []);
  }

  /// `Area Name Arabic`
  String get areaar {
    return Intl.message('Area Name Arabic', name: 'areaar', desc: '', args: []);
  }

  /// `Area Name English`
  String get areaen {
    return Intl.message(
      'Area Name English',
      name: 'areaen',
      desc: '',
      args: [],
    );
  }

  /// `Add Application feature`
  String get AddApplicationfeature {
    return Intl.message(
      'Add Application feature',
      name: 'AddApplicationfeature',
      desc: '',
      args: [],
    );
  }

  /// `Remove from category feature`
  String get Removefromcategoryfeature {
    return Intl.message(
      'Remove from category feature',
      name: 'Removefromcategoryfeature',
      desc: '',
      args: [],
    );
  }

  /// `Requests`
  String get Requests {
    return Intl.message('Requests', name: 'Requests', desc: '', args: []);
  }

  /// `All Requests`
  String get AllRequests {
    return Intl.message(
      'All Requests',
      name: 'AllRequests',
      desc: '',
      args: [],
    );
  }

  /// `Number of People`
  String get NumberofPeople {
    return Intl.message(
      'Number of People',
      name: 'NumberofPeople',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get Status {
    return Intl.message('Status', name: 'Status', desc: '', args: []);
  }

  /// `Requestedon`
  String get Requestedon {
    return Intl.message('Requestedon', name: 'Requestedon', desc: '', args: []);
  }

  /// `Agent`
  String get Agent {
    return Intl.message('Agent', name: 'Agent', desc: '', args: []);
  }

  /// `Number of people`
  String get Numberofpeople {
    return Intl.message(
      'Number of people',
      name: 'Numberofpeople',
      desc: '',
      args: [],
    );
  }

  /// `TotalPrice`
  String get TotalPrice {
    return Intl.message('TotalPrice', name: 'TotalPrice', desc: '', args: []);
  }

  /// `Edit Price`
  String get editPrice {
    return Intl.message('Edit Price', name: 'editPrice', desc: '', args: []);
  }

  /// `Agent name`
  String get Agentname {
    return Intl.message('Agent name', name: 'Agentname', desc: '', args: []);
  }

  /// `Tesla Model3`
  String get TeslaModel3 {
    return Intl.message(
      'Tesla Model3',
      name: 'TeslaModel3',
      desc: '',
      args: [],
    );
  }

  /// `Call client`
  String get Callclient {
    return Intl.message('Call client', name: 'Callclient', desc: '', args: []);
  }

  /// `Call Agent`
  String get CallAgent {
    return Intl.message('Call Agent', name: 'CallAgent', desc: '', args: []);
  }

  /// `Request Details`
  String get RequestDetails {
    return Intl.message(
      'Request Details',
      name: 'RequestDetails',
      desc: '',
      args: [],
    );
  }

  /// `Requeste date`
  String get Requesteddate {
    return Intl.message(
      'Requeste date',
      name: 'Requesteddate',
      desc: '',
      args: [],
    );
  }

  /// `Private Driver`
  String get PrivateDriver {
    return Intl.message(
      'Private Driver',
      name: 'PrivateDriver',
      desc: '',
      args: [],
    );
  }

  /// `Pickup/DropOff`
  String get PickupDropOff {
    return Intl.message(
      'Pickup/DropOff',
      name: 'PickupDropOff',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get Note {
    return Intl.message('Note', name: 'Note', desc: '', args: []);
  }

  /// `Number Of Days`
  String get NumberOfDays {
    return Intl.message(
      'Number Of Days',
      name: 'NumberOfDays',
      desc: '',
      args: [],
    );
  }

  /// `Agent Note`
  String get AgentNote {
    return Intl.message('Agent Note', name: 'AgentNote', desc: '', args: []);
  }

  /// `Agreement Total Price`
  String get AgreementTotalPrice {
    return Intl.message(
      'Agreement Total Price',
      name: 'AgreementTotalPrice',
      desc: '',
      args: [],
    );
  }

  /// `Holiday Home 1`
  String get HolidayHome1 {
    return Intl.message(
      'Holiday Home 1',
      name: 'HolidayHome1',
      desc: '',
      args: [],
    );
  }

  /// `Al Malek Fahed Street`
  String get AlMalekFahedStreet {
    return Intl.message(
      'Al Malek Fahed Street',
      name: 'AlMalekFahedStreet',
      desc: '',
      args: [],
    );
  }

  /// `Price Breakdown`
  String get PriceBreakdown {
    return Intl.message(
      'Price Breakdown',
      name: 'PriceBreakdown',
      desc: '',
      args: [],
    );
  }

  /// `Price`
  String get Price {
    return Intl.message('Price', name: 'Price', desc: '', args: []);
  }

  /// `Private drive price`
  String get priprice {
    return Intl.message(
      'Private drive price',
      name: 'priprice',
      desc: '',
      args: [],
    );
  }

  /// `Please I want a very clean and big car.`
  String get PleaseIwantaverycleanandbigcar {
    return Intl.message(
      'Please I want a very clean and big car.',
      name: 'PleaseIwantaverycleanandbigcar',
      desc: '',
      args: [],
    );
  }

  /// `Subtotal`
  String get Subtotal {
    return Intl.message('Subtotal', name: 'Subtotal', desc: '', args: []);
  }

  /// `Promo Subtotal`
  String get PromoSubtotal {
    return Intl.message(
      'Promo Subtotal',
      name: 'PromoSubtotal',
      desc: '',
      args: [],
    );
  }

  /// `Vat`
  String get Vat {
    return Intl.message('Vat', name: 'Vat', desc: '', args: []);
  }

  /// `Accepted`
  String get Accepted {
    return Intl.message('Accepted', name: 'Accepted', desc: '', args: []);
  }

  /// `Pending`
  String get Pending {
    return Intl.message('Pending', name: 'Pending', desc: '', args: []);
  }

  /// `Arabic Terms And Condtion`
  String get termar {
    return Intl.message(
      'Arabic Terms And Condtion',
      name: 'termar',
      desc: '',
      args: [],
    );
  }

  /// `English Terms And Condtion`
  String get termen {
    return Intl.message(
      'English Terms And Condtion',
      name: 'termen',
      desc: '',
      args: [],
    );
  }

  /// `YouTube`
  String get YouTube {
    return Intl.message('YouTube', name: 'YouTube', desc: '', args: []);
  }

  /// `Instagram`
  String get Instagram {
    return Intl.message('Instagram', name: 'Instagram', desc: '', args: []);
  }

  /// `Denied`
  String get Denied {
    return Intl.message('Denied', name: 'Denied', desc: '', args: []);
  }

  /// `Canceled`
  String get Canceled {
    return Intl.message('Canceled', name: 'Canceled', desc: '', args: []);
  }

  /// `Restaurants`
  String get Restaurants {
    return Intl.message('Restaurants', name: 'Restaurants', desc: '', args: []);
  }

  /// `Number of days`
  String get Numberofdays {
    return Intl.message(
      'Number of days',
      name: 'Numberofdays',
      desc: '',
      args: [],
    );
  }

  /// `Tourism fee (5Nights)`
  String get Tourismfee {
    return Intl.message(
      'Tourism fee (5Nights)',
      name: 'Tourismfee',
      desc: '',
      args: [],
    );
  }

  /// `Agent note`
  String get Agentnote {
    return Intl.message('Agent note', name: 'Agentnote', desc: '', args: []);
  }

  /// `Fee`
  String get Fee {
    return Intl.message('Fee', name: 'Fee', desc: '', args: []);
  }

  /// `Other settings`
  String get Othersettings {
    return Intl.message(
      'Other settings',
      name: 'Othersettings',
      desc: '',
      args: [],
    );
  }

  /// `Types`
  String get Types {
    return Intl.message('Types', name: 'Types', desc: '', args: []);
  }

  /// `Manage features, types, brands and other stuff`
  String get manage {
    return Intl.message(
      'Manage features, types, brands and other stuff',
      name: 'manage',
      desc: '',
      args: [],
    );
  }

  /// `Car brads`
  String get Carbrads {
    return Intl.message('Car brads', name: 'Carbrads', desc: '', args: []);
  }

  /// `Configuration`
  String get Configuration {
    return Intl.message(
      'Configuration',
      name: 'Configuration',
      desc: '',
      args: [],
    );
  }

  /// `All Promo Codes`
  String get AllPromoCodes {
    return Intl.message(
      'All Promo Codes',
      name: 'AllPromoCodes',
      desc: '',
      args: [],
    );
  }

  /// `20 users used Promo Code`
  String get usersusedPromoCode {
    return Intl.message(
      '20 users used Promo Code',
      name: 'usersusedPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Add new Promo Code`
  String get AddnewPromoCode {
    return Intl.message(
      'Add new Promo Code',
      name: 'AddnewPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `All Promo codes`
  String get AllPromocodes {
    return Intl.message(
      'All Promo codes',
      name: 'AllPromocodes',
      desc: '',
      args: [],
    );
  }

  /// `Discount`
  String get Discount {
    return Intl.message('Discount', name: 'Discount', desc: '', args: []);
  }

  /// `Code`
  String get Code {
    return Intl.message('Code', name: 'Code', desc: '', args: []);
  }

  /// `End at`
  String get endat {
    return Intl.message('End at', name: 'endat', desc: '', args: []);
  }

  /// `Promo Codes`
  String get promo {
    return Intl.message('Promo Codes', name: 'promo', desc: '', args: []);
  }

  /// `Discount Percentage`
  String get DiscountPercentage {
    return Intl.message(
      'Discount Percentage',
      name: 'DiscountPercentage',
      desc: '',
      args: [],
    );
  }

  /// `Exit`
  String get Exit {
    return Intl.message('Exit', name: 'Exit', desc: '', args: []);
  }

  /// `Save Changes`
  String get SaveChanges {
    return Intl.message(
      'Save Changes',
      name: 'SaveChanges',
      desc: '',
      args: [],
    );
  }

  /// `Add New Promo Code`
  String get AddNewPromoCode {
    return Intl.message(
      'Add New Promo Code',
      name: 'AddNewPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Delete Promo Code`
  String get DeletePromoCode {
    return Intl.message(
      'Delete Promo Code',
      name: 'DeletePromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Profile was updated successfuly`
  String get updatepro {
    return Intl.message(
      'Profile was updated successfuly',
      name: 'updatepro',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this promo code`
  String get Areyousureyouwanttodeletethispromocode {
    return Intl.message(
      'Are you sure you want to delete this promo code',
      name: 'Areyousureyouwanttodeletethispromocode',
      desc: '',
      args: [],
    );
  }

  /// `Admin Account Information`
  String get AdminAccountInformation {
    return Intl.message(
      'Admin Account Information',
      name: 'AdminAccountInformation',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get PhoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'PhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong, please try again later`
  String get wrong {
    return Intl.message(
      'Something went wrong, please try again later',
      name: 'wrong',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this Holiday Home, if yes you won’t be able to see it again.`
  String get deletehome {
    return Intl.message(
      'Are you sure you want to delete this Holiday Home, if yes you won’t be able to see it again.',
      name: 'deletehome',
      desc: '',
      args: [],
    );
  }

  /// `Facebook page`
  String get face {
    return Intl.message('Facebook page', name: 'face', desc: '', args: []);
  }

  /// `Feature Name English`
  String get feaen {
    return Intl.message(
      'Feature Name English',
      name: 'feaen',
      desc: '',
      args: [],
    );
  }

  /// `Feature Name Arabic`
  String get feaar {
    return Intl.message(
      'Feature Name Arabic',
      name: 'feaar',
      desc: '',
      args: [],
    );
  }

  /// `Type Name English`
  String get typeen {
    return Intl.message(
      'Type Name English',
      name: 'typeen',
      desc: '',
      args: [],
    );
  }

  /// `Type Name Arabic`
  String get typear {
    return Intl.message('Type Name Arabic', name: 'typear', desc: '', args: []);
  }

  /// `Please enter arabic name`
  String get enteraran {
    return Intl.message(
      'Please enter arabic name',
      name: 'enteraran',
      desc: '',
      args: [],
    );
  }

  /// `Delete Property Status`
  String get deletepropertystatus {
    return Intl.message(
      'Delete Property Status',
      name: 'deletepropertystatus',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this property status, if yes you won’t be able to see it again.`
  String get deletepropertystatusDesc {
    return Intl.message(
      'Are you sure you want to delete this property status, if yes you won’t be able to see it again.',
      name: 'deletepropertystatusDesc',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this`
  String get areyousureyouwanttodelete {
    return Intl.message(
      'Are you sure you want to delete this',
      name: 'areyousureyouwanttodelete',
      desc: '',
      args: [],
    );
  }

  /// `Please enter English name`
  String get enterenn {
    return Intl.message(
      'Please enter English name',
      name: 'enterenn',
      desc: '',
      args: [],
    );
  }

  /// `Add Property Status`
  String get AddPropertyStatus {
    return Intl.message(
      'Add Property Status',
      name: 'AddPropertyStatus',
      desc: '',
      args: [],
    );
  }

  /// `Please enter category`
  String get enterc {
    return Intl.message(
      'Please enter category',
      name: 'enterc',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this agent`
  String get deleteag {
    return Intl.message(
      'Are you sure you want to delete this agent',
      name: 'deleteag',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this price plan?`
  String get areYouSureDeletePricePlan {
    return Intl.message(
      'Are you sure you want to delete this price plan?',
      name: 'areYouSureDeletePricePlan',
      desc: '',
      args: [],
    );
  }

  /// `Contract Finish Date`
  String get ContractFinishDate {
    return Intl.message(
      'Contract Finish Date',
      name: 'ContractFinishDate',
      desc: '',
      args: [],
    );
  }

  /// `Instagram Page`
  String get insta {
    return Intl.message('Instagram Page', name: 'insta', desc: '', args: []);
  }

  /// `Select images please`
  String get selectimg {
    return Intl.message(
      'Select images please',
      name: 'selectimg',
      desc: '',
      args: [],
    );
  }

  /// `Select main video please`
  String get selectvid {
    return Intl.message(
      'Select main video please',
      name: 'selectvid',
      desc: '',
      args: [],
    );
  }

  /// `Added success`
  String get added {
    return Intl.message('Added success', name: 'added', desc: '', args: []);
  }

  /// `Fill required fields above please`
  String get fill {
    return Intl.message(
      'Fill required fields above please',
      name: 'fill',
      desc: '',
      args: [],
    );
  }

  /// `Change Password`
  String get ChangePassword {
    return Intl.message(
      'Change Password',
      name: 'ChangePassword',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get Save {
    return Intl.message('Save', name: 'Save', desc: '', args: []);
  }

  /// `Edited Successfully`
  String get editedSuccessfully {
    return Intl.message(
      'Edited Successfully',
      name: 'editedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `All Features`
  String get AllFeatures {
    return Intl.message(
      'All Features',
      name: 'AllFeatures',
      desc: '',
      args: [],
    );
  }

  /// `Delete Area`
  String get DeleteArea {
    return Intl.message('Delete Area', name: 'DeleteArea', desc: '', args: []);
  }

  /// `Search`
  String get Search {
    return Intl.message('Search', name: 'Search', desc: '', args: []);
  }

  /// `Please enter the email field`
  String get entere {
    return Intl.message(
      'Please enter the email field',
      name: 'entere',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the password field`
  String get enterp {
    return Intl.message(
      'Please enter the password field',
      name: 'enterp',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the  confirm new password field`
  String get enterpc {
    return Intl.message(
      'Please enter the  confirm new password field',
      name: 'enterpc',
      desc: '',
      args: [],
    );
  }

  /// `Burj Khalifa view`
  String get BurjKhalifaview {
    return Intl.message(
      'Burj Khalifa view',
      name: 'BurjKhalifaview',
      desc: '',
      args: [],
    );
  }

  /// `Big rooms`
  String get Bigrooms {
    return Intl.message('Big rooms', name: 'Bigrooms', desc: '', args: []);
  }

  /// `Balcony`
  String get Balcony {
    return Intl.message('Balcony', name: 'Balcony', desc: '', args: []);
  }

  /// `Add New Feature`
  String get AddNewFeature {
    return Intl.message(
      'Add New Feature',
      name: 'AddNewFeature',
      desc: '',
      args: [],
    );
  }

  /// `Choose Category`
  String get ChooseCategory {
    return Intl.message(
      'Choose Category',
      name: 'ChooseCategory',
      desc: '',
      args: [],
    );
  }

  /// `Sea view`
  String get Seaview {
    return Intl.message('Sea view', name: 'Seaview', desc: '', args: []);
  }

  /// `feature Name`
  String get featureName {
    return Intl.message(
      'feature Name',
      name: 'featureName',
      desc: '',
      args: [],
    );
  }

  /// `Delete feature`
  String get Deletefeature {
    return Intl.message(
      'Delete feature',
      name: 'Deletefeature',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this feature`
  String get Areyousureyouwanttodeletethisfeature {
    return Intl.message(
      'Are you sure you want to delete this feature',
      name: 'Areyousureyouwanttodeletethisfeature',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete feature`
  String get yesDeletefeature {
    return Intl.message(
      'yes,Delete feature',
      name: 'yesDeletefeature',
      desc: '',
      args: [],
    );
  }

  /// `Holiday Home`
  String get HolidayHome {
    return Intl.message(
      'Holiday Home',
      name: 'HolidayHome',
      desc: '',
      args: [],
    );
  }

  /// `Activities`
  String get Activities {
    return Intl.message('Activities', name: 'Activities', desc: '', args: []);
  }

  /// `All types`
  String get Alltypes {
    return Intl.message('All types', name: 'Alltypes', desc: '', args: []);
  }

  /// `Add new type`
  String get Addnewtype {
    return Intl.message('Add new type', name: 'Addnewtype', desc: '', args: []);
  }

  /// `Category`
  String get Category {
    return Intl.message('Category', name: 'Category', desc: '', args: []);
  }

  /// ` Property`
  String get Property {
    return Intl.message(' Property', name: 'Property', desc: '', args: []);
  }

  /// `Filter`
  String get Filter {
    return Intl.message('Filter', name: 'Filter', desc: '', args: []);
  }

  /// `Reset`
  String get Reset {
    return Intl.message('Reset', name: 'Reset', desc: '', args: []);
  }

  /// `All Categories`
  String get AllCategories {
    return Intl.message(
      'All Categories',
      name: 'AllCategories',
      desc: '',
      args: [],
    );
  }

  /// `villa`
  String get villa {
    return Intl.message('villa', name: 'villa', desc: '', args: []);
  }

  /// `Chalet`
  String get Chalet {
    return Intl.message('Chalet', name: 'Chalet', desc: '', args: []);
  }

  /// `Farm`
  String get Farm {
    return Intl.message('Farm', name: 'Farm', desc: '', args: []);
  }

  /// `Properety`
  String get Properety {
    return Intl.message('Properety', name: 'Properety', desc: '', args: []);
  }

  /// `Delete type`
  String get Deletetype {
    return Intl.message('Delete type', name: 'Deletetype', desc: '', args: []);
  }

  /// `Are you sure you want to delete this type`
  String get Areyousureyouwanttodeletethistype {
    return Intl.message(
      'Are you sure you want to delete this type',
      name: 'Areyousureyouwanttodeletethistype',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete type`
  String get yesDeletetype {
    return Intl.message(
      'yes,Delete type',
      name: 'yesDeletetype',
      desc: '',
      args: [],
    );
  }

  /// `Car Brands`
  String get CarBrands {
    return Intl.message('Car Brands', name: 'CarBrands', desc: '', args: []);
  }

  /// `All Car Brands`
  String get AllCarBrands {
    return Intl.message(
      'All Car Brands',
      name: 'AllCarBrands',
      desc: '',
      args: [],
    );
  }

  /// `Toyota`
  String get Toyota {
    return Intl.message('Toyota', name: 'Toyota', desc: '', args: []);
  }

  /// `Tesla`
  String get Tesla {
    return Intl.message('Tesla', name: 'Tesla', desc: '', args: []);
  }

  /// `Brand`
  String get Brand {
    return Intl.message('Brand', name: 'Brand', desc: '', args: []);
  }

  /// `BMW`
  String get BMW {
    return Intl.message('BMW', name: 'BMW', desc: '', args: []);
  }

  /// `Chevrolet`
  String get Chevrolet {
    return Intl.message('Chevrolet', name: 'Chevrolet', desc: '', args: []);
  }

  /// `Kia`
  String get Kia {
    return Intl.message('Kia', name: 'Kia', desc: '', args: []);
  }

  /// `Range Rover`
  String get RangeRover {
    return Intl.message('Range Rover', name: 'RangeRover', desc: '', args: []);
  }

  /// `Add New Brand`
  String get AddNewBrand {
    return Intl.message(
      'Add New Brand',
      name: 'AddNewBrand',
      desc: '',
      args: [],
    );
  }

  /// `Brand Name`
  String get BrandName {
    return Intl.message('Brand Name', name: 'BrandName', desc: '', args: []);
  }

  /// `Delete Brand`
  String get DeleteBrand {
    return Intl.message(
      'Delete Brand',
      name: 'DeleteBrand',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this brand`
  String get Areyousureyouwanttodeletethisbrand {
    return Intl.message(
      'Are you sure you want to delete this brand',
      name: 'Areyousureyouwanttodeletethisbrand',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete brand`
  String get yesDeletebrand {
    return Intl.message(
      'yes,Delete brand',
      name: 'yesDeletebrand',
      desc: '',
      args: [],
    );
  }

  /// `Large Selection of Luxury Hotels for you and your loved one،Book online today ..`
  String get LargeSelectionofLuxuryHotelsforyouandyourlovedoneBookonlinetoday {
    return Intl.message(
      'Large Selection of Luxury Hotels for you and your loved one،Book online today ..',
      name: 'LargeSelectionofLuxuryHotelsforyouandyourlovedoneBookonlinetoday',
      desc: '',
      args: [],
    );
  }

  /// `Edit images`
  String get Editimages {
    return Intl.message('Edit images', name: 'Editimages', desc: '', args: []);
  }

  /// `Edit Reel videos`
  String get EditReelvideos {
    return Intl.message(
      'Edit Reel videos',
      name: 'EditReelvideos',
      desc: '',
      args: [],
    );
  }

  /// `View Discussion`
  String get ViewDiscussion {
    return Intl.message(
      'View Discussion',
      name: 'ViewDiscussion',
      desc: '',
      args: [],
    );
  }

  /// `Delete Reel Video`
  String get DeleteReelVideo {
    return Intl.message(
      'Delete Reel Video',
      name: 'DeleteReelVideo',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get Cancel {
    return Intl.message('Cancel', name: 'Cancel', desc: '', args: []);
  }

  /// `Are you sure you want to delete this reel video`
  String get Areyousureyouwanttodeletethisreelvideo {
    return Intl.message(
      'Are you sure you want to delete this reel video',
      name: 'Areyousureyouwanttodeletethisreelvideo',
      desc: '',
      args: [],
    );
  }

  /// `yes,Deletereel video`
  String get yesDeletereelvideo {
    return Intl.message(
      'yes,Deletereel video',
      name: 'yesDeletereelvideo',
      desc: '',
      args: [],
    );
  }

  /// `Upload images`
  String get Uploadimages {
    return Intl.message(
      'Upload images',
      name: 'Uploadimages',
      desc: '',
      args: [],
    );
  }

  /// `Delete Video`
  String get deletev {
    return Intl.message('Delete Video', name: 'deletev', desc: '', args: []);
  }

  /// `Upload reel videos`
  String get Uploadreelvideos {
    return Intl.message(
      'Upload reel videos',
      name: 'Uploadreelvideos',
      desc: '',
      args: [],
    );
  }

  /// `Tab here to upload image's`
  String get Tabheretouploadimage {
    return Intl.message(
      'Tab here to upload image\'s',
      name: 'Tabheretouploadimage',
      desc: '',
      args: [],
    );
  }

  /// `Upload Photos`
  String get UploadPhotos {
    return Intl.message(
      'Upload Photos',
      name: 'UploadPhotos',
      desc: '',
      args: [],
    );
  }

  /// `Tab here to upload video/s`
  String get Tabheretouploadvideo {
    return Intl.message(
      'Tab here to upload video/s',
      name: 'Tabheretouploadvideo',
      desc: '',
      args: [],
    );
  }

  /// `Upload videos`
  String get Uploadvideos {
    return Intl.message(
      'Upload videos',
      name: 'Uploadvideos',
      desc: '',
      args: [],
    );
  }

  /// `Re-Upload reel video`
  String get ReUploadreelvideo {
    return Intl.message(
      'Re-Upload reel video',
      name: 'ReUploadreelvideo',
      desc: '',
      args: [],
    );
  }

  /// `Tab here to reel video`
  String get Tabheretoreelvideo {
    return Intl.message(
      'Tab here to reel video',
      name: 'Tabheretoreelvideo',
      desc: '',
      args: [],
    );
  }

  /// `Discussion`
  String get Discussion {
    return Intl.message('Discussion', name: 'Discussion', desc: '', args: []);
  }

  /// `Tab here to upload photo`
  String get tapp {
    return Intl.message(
      'Tab here to upload photo',
      name: 'tapp',
      desc: '',
      args: [],
    );
  }

  /// `This place is everything you need for the best stay, Perfect location, view and atmosphere.`
  String get Thisplaceiseverythingyouneedforthebeststay {
    return Intl.message(
      'This place is everything you need for the best stay, Perfect location, view and atmosphere.',
      name: 'Thisplaceiseverythingyouneedforthebeststay',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Holiday Home Name`
  String get arahome {
    return Intl.message(
      'Arabic Holiday Home Name',
      name: 'arahome',
      desc: '',
      args: [],
    );
  }

  /// `English Holiday Home Name`
  String get enhome {
    return Intl.message(
      'English Holiday Home Name',
      name: 'enhome',
      desc: '',
      args: [],
    );
  }

  /// `Hotel Name`
  String get HotelName {
    return Intl.message('Hotel Name', name: 'HotelName', desc: '', args: []);
  }

  /// `Price Range Per Night`
  String get PriceRangePerNight {
    return Intl.message(
      'Price Range Per Night',
      name: 'PriceRangePerNight',
      desc: '',
      args: [],
    );
  }

  /// `Join thediscussion`
  String get Jointhediscussion {
    return Intl.message(
      'Join thediscussion',
      name: 'Jointhediscussion',
      desc: '',
      args: [],
    );
  }

  /// `Property size`
  String get holidaysi {
    return Intl.message('Property size', name: 'holidaysi', desc: '', args: []);
  }

  /// `Number of bedrooms`
  String get numroom {
    return Intl.message(
      'Number of bedrooms',
      name: 'numroom',
      desc: '',
      args: [],
    );
  }

  /// `Edi tcomment`
  String get Editcomment {
    return Intl.message(
      'Edi tcomment',
      name: 'Editcomment',
      desc: '',
      args: [],
    );
  }

  /// `Translate Comment`
  String get TranslateComment {
    return Intl.message(
      'Translate Comment',
      name: 'TranslateComment',
      desc: '',
      args: [],
    );
  }

  /// `Delete Comment`
  String get DeleteComment {
    return Intl.message(
      'Delete Comment',
      name: 'DeleteComment',
      desc: '',
      args: [],
    );
  }

  /// `Hide Comment`
  String get HideComment {
    return Intl.message(
      'Hide Comment',
      name: 'HideComment',
      desc: '',
      args: [],
    );
  }

  /// `Holiday Homes`
  String get HolidayHomes {
    return Intl.message(
      'Holiday Homes',
      name: 'HolidayHomes',
      desc: '',
      args: [],
    );
  }

  /// `All holiday homes`
  String get Allholidayhomes {
    return Intl.message(
      'All holiday homes',
      name: 'Allholidayhomes',
      desc: '',
      args: [],
    );
  }

  /// `Add new Holiday Home`
  String get AddnewHolidayHome {
    return Intl.message(
      'Add new Holiday Home',
      name: 'AddnewHolidayHome',
      desc: '',
      args: [],
    );
  }

  /// `Basic information`
  String get Basicinformation {
    return Intl.message(
      'Basic information',
      name: 'Basicinformation',
      desc: '',
      args: [],
    );
  }

  /// `AED`
  String get AED {
    return Intl.message('AED', name: 'AED', desc: '', args: []);
  }

  /// `Arabic Car Rental Name`
  String get arabicnum {
    return Intl.message(
      'Arabic Car Rental Name',
      name: 'arabicnum',
      desc: '',
      args: [],
    );
  }

  /// `English Car Rental Name`
  String get engnum {
    return Intl.message(
      'English Car Rental Name',
      name: 'engnum',
      desc: '',
      args: [],
    );
  }

  /// `From`
  String get From {
    return Intl.message('From', name: 'From', desc: '', args: []);
  }

  /// `Filtered As`
  String get filteras {
    return Intl.message('Filtered As', name: 'filteras', desc: '', args: []);
  }

  /// `Arabic Activity Name`
  String get activityara {
    return Intl.message(
      'Arabic Activity Name',
      name: 'activityara',
      desc: '',
      args: [],
    );
  }

  /// `To`
  String get To {
    return Intl.message('To', name: 'To', desc: '', args: []);
  }

  /// `Arabic Description`
  String get desara {
    return Intl.message(
      'Arabic Description',
      name: 'desara',
      desc: '',
      args: [],
    );
  }

  /// `English Description`
  String get deaen {
    return Intl.message(
      'English Description',
      name: 'deaen',
      desc: '',
      args: [],
    );
  }

  /// `English Activity Name`
  String get activityeng {
    return Intl.message(
      'English Activity Name',
      name: 'activityeng',
      desc: '',
      args: [],
    );
  }

  /// `Features`
  String get Features {
    return Intl.message('Features', name: 'Features', desc: '', args: []);
  }

  /// `There are no Features`
  String get nofea {
    return Intl.message(
      'There are no Features',
      name: 'nofea',
      desc: '',
      args: [],
    );
  }

  /// `Set all features`
  String get Setallfeatures {
    return Intl.message(
      'Set all features',
      name: 'Setallfeatures',
      desc: '',
      args: [],
    );
  }

  /// `Number of bedrooms `
  String get Numberofbedrooms {
    return Intl.message(
      'Number of bedrooms ',
      name: 'Numberofbedrooms',
      desc: '',
      args: [],
    );
  }

  /// `Home Size`
  String get HomeSize {
    return Intl.message('Home Size', name: 'HomeSize', desc: '', args: []);
  }

  /// `Location Information `
  String get LocationInformation {
    return Intl.message(
      'Location Information ',
      name: 'LocationInformation',
      desc: '',
      args: [],
    );
  }

  /// ` Location `
  String get Location {
    return Intl.message(' Location ', name: 'Location', desc: '', args: []);
  }

  /// `Set Location on map?`
  String get SetLocationonmap {
    return Intl.message(
      'Set Location on map?',
      name: 'SetLocationonmap',
      desc: '',
      args: [],
    );
  }

  /// `Upload Main Video`
  String get UploadMainVideo {
    return Intl.message(
      'Upload Main Video',
      name: 'UploadMainVideo',
      desc: '',
      args: [],
    );
  }

  /// `Upload Main Arabic Video`
  String get UploadMainVideoAr {
    return Intl.message(
      'Upload Main Arabic Video',
      name: 'UploadMainVideoAr',
      desc: '',
      args: [],
    );
  }

  /// `Tab here to upload main video`
  String get Tabheretouploadmainvideo {
    return Intl.message(
      'Tab here to upload main video',
      name: 'Tabheretouploadmainvideo',
      desc: '',
      args: [],
    );
  }

  /// `Tab here to upload main arabic video `
  String get TabheretouploadmainvideoAr {
    return Intl.message(
      'Tab here to upload main arabic video ',
      name: 'TabheretouploadmainvideoAr',
      desc: '',
      args: [],
    );
  }

  /// `Assign as featured video in category page`
  String get Assignasfeaturedvideoincatpage {
    return Intl.message(
      'Assign as featured video in category page',
      name: 'Assignasfeaturedvideoincatpage',
      desc: '',
      args: [],
    );
  }

  /// `Assign as featured video in home page`
  String get Assignasfeaturedvideoinhomepage {
    return Intl.message(
      'Assign as featured video in home page',
      name: 'Assignasfeaturedvideoinhomepage',
      desc: '',
      args: [],
    );
  }

  /// `Upload Reel/s`
  String get UploadReel {
    return Intl.message(
      'Upload Reel/s',
      name: 'UploadReel',
      desc: '',
      args: [],
    );
  }

  /// `Tab here to upload reel/s`
  String get Tabheretouploadreel {
    return Intl.message(
      'Tab here to upload reel/s',
      name: 'Tabheretouploadreel',
      desc: '',
      args: [],
    );
  }

  /// `Upload Image/s`
  String get UploadImage {
    return Intl.message(
      'Upload Image/s',
      name: 'UploadImage',
      desc: '',
      args: [],
    );
  }

  /// `Extra Information`
  String get ExtraInformation {
    return Intl.message(
      'Extra Information',
      name: 'ExtraInformation',
      desc: '',
      args: [],
    );
  }

  /// `Google Review Link`
  String get GoogleReviewLink {
    return Intl.message(
      'Google Review Link',
      name: 'GoogleReviewLink',
      desc: '',
      args: [],
    );
  }

  /// `Car Rental`
  String get CarRental {
    return Intl.message('Car Rental', name: 'CarRental', desc: '', args: []);
  }

  /// `Reviews`
  String get Reviews {
    return Intl.message('Reviews', name: 'Reviews', desc: '', args: []);
  }

  /// `AllRentalCars`
  String get AllRentalCars {
    return Intl.message(
      'AllRentalCars',
      name: 'AllRentalCars',
      desc: '',
      args: [],
    );
  }

  /// `SearchCars`
  String get SearchCars {
    return Intl.message('SearchCars', name: 'SearchCars', desc: '', args: []);
  }

  /// `+ Add New Rental Cars`
  String get AddNewRentalCars {
    return Intl.message(
      '+ Add New Rental Cars',
      name: 'AddNewRentalCars',
      desc: '',
      args: [],
    );
  }

  /// `Basic Information`
  String get BasicInformation {
    return Intl.message(
      'Basic Information',
      name: 'BasicInformation',
      desc: '',
      args: [],
    );
  }

  /// `Car Name`
  String get CarName {
    return Intl.message('Car Name', name: 'CarName', desc: '', args: []);
  }

  /// `Write down your description`
  String get Writedownyourdescription {
    return Intl.message(
      'Write down your description',
      name: 'Writedownyourdescription',
      desc: '',
      args: [],
    );
  }

  /// `Price Range Per Day`
  String get PriceRangePerDay {
    return Intl.message(
      'Price Range Per Day',
      name: 'PriceRangePerDay',
      desc: '',
      args: [],
    );
  }

  /// `Driver Price Per Day`
  String get DriverPricePerDay {
    return Intl.message(
      'Driver Price Per Day',
      name: 'DriverPricePerDay',
      desc: '',
      args: [],
    );
  }

  /// `Car Production Year`
  String get CarProductionYear {
    return Intl.message(
      'Car Production Year',
      name: 'CarProductionYear',
      desc: '',
      args: [],
    );
  }

  /// `Delete Holiday home`
  String get DeleteHolidayhome {
    return Intl.message(
      'Delete Holiday home',
      name: 'DeleteHolidayhome',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this Holiday home`
  String get AreyousureyouwanttodeletethisHolidayhome {
    return Intl.message(
      'Are you sure you want to delete this Holiday home',
      name: 'AreyousureyouwanttodeletethisHolidayhome',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete Holiday home`
  String get yesDeleteHolidayhome {
    return Intl.message(
      'yes,Delete Holiday home',
      name: 'yesDeleteHolidayhome',
      desc: '',
      args: [],
    );
  }

  /// `Destination`
  String get Destination {
    return Intl.message('Destination', name: 'Destination', desc: '', args: []);
  }

  /// `Destinations`
  String get Destinations {
    return Intl.message(
      'Destinations',
      name: 'Destinations',
      desc: '',
      args: [],
    );
  }

  /// `Search RMS Discount`
  String get searchRmsDiscount {
    return Intl.message(
      'Search RMS Discount',
      name: 'searchRmsDiscount',
      desc: '',
      args: [],
    );
  }

  /// `Select RMS Discount`
  String get selectRmsDiscount {
    return Intl.message(
      'Select RMS Discount',
      name: 'selectRmsDiscount',
      desc: '',
      args: [],
    );
  }

  /// `All Destinations`
  String get AllDestinations {
    return Intl.message(
      'All Destinations',
      name: 'AllDestinations',
      desc: '',
      args: [],
    );
  }

  /// `Car rental`
  String get Carrental {
    return Intl.message('Car rental', name: 'Carrental', desc: '', args: []);
  }

  /// `All Rental cars`
  String get AllRentalcars {
    return Intl.message(
      'All Rental cars',
      name: 'AllRentalcars',
      desc: '',
      args: [],
    );
  }

  /// `Search cars`
  String get Searchcars {
    return Intl.message('Search cars', name: 'Searchcars', desc: '', args: []);
  }

  /// `Add new rental cars`
  String get Addnewrentalcars {
    return Intl.message(
      'Add new rental cars',
      name: 'Addnewrentalcars',
      desc: '',
      args: [],
    );
  }

  /// `Add new rental car`
  String get Addnewrentalcar {
    return Intl.message(
      'Add new rental car',
      name: 'Addnewrentalcar',
      desc: '',
      args: [],
    );
  }

  /// `Car name`
  String get Carname {
    return Intl.message('Car name', name: 'Carname', desc: '', args: []);
  }

  /// `Description`
  String get Description {
    return Intl.message('Description', name: 'Description', desc: '', args: []);
  }

  /// `Price range per day`
  String get Pricerangeperday {
    return Intl.message(
      'Price range per day',
      name: 'Pricerangeperday',
      desc: '',
      args: [],
    );
  }

  /// `Driver Price per day`
  String get DriverPriceperday {
    return Intl.message(
      'Driver Price per day',
      name: 'DriverPriceperday',
      desc: '',
      args: [],
    );
  }

  /// `Delete Rental car`
  String get DeleteRentalcar {
    return Intl.message(
      'Delete Rental car',
      name: 'DeleteRentalcar',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this Video,if yes you wont be able to see again`
  String get delemsgv {
    return Intl.message(
      'Are you sure you want to delete this Video,if yes you wont be able to see again',
      name: 'delemsgv',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this rental car,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethisrentalcar {
    return Intl.message(
      'Are you sure you want to delete this rental car,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethisrentalcar',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete rental car`
  String get yesDeleterentalcar {
    return Intl.message(
      'yes,Delete rental car',
      name: 'yesDeleterentalcar',
      desc: '',
      args: [],
    );
  }

  /// `Properties`
  String get Properties {
    return Intl.message('Properties', name: 'Properties', desc: '', args: []);
  }

  /// `All Properties`
  String get AllProperties {
    return Intl.message(
      'All Properties',
      name: 'AllProperties',
      desc: '',
      args: [],
    );
  }

  /// `Add New Restaurant`
  String get addres {
    return Intl.message(
      'Add New Restaurant',
      name: 'addres',
      desc: '',
      args: [],
    );
  }

  /// `Add new Property`
  String get AddnewProperty {
    return Intl.message(
      'Add new Property',
      name: 'AddnewProperty',
      desc: '',
      args: [],
    );
  }

  /// `Search destinations and locations`
  String get Searchplacesandlocations {
    return Intl.message(
      'Search destinations and locations',
      name: 'Searchplacesandlocations',
      desc: '',
      args: [],
    );
  }

  /// `Add New Destination`
  String get AddNewDestination {
    return Intl.message(
      'Add New Destination',
      name: 'AddNewDestination',
      desc: '',
      args: [],
    );
  }

  /// `Property name`
  String get Propertyname {
    return Intl.message(
      'Property name',
      name: 'Propertyname',
      desc: '',
      args: [],
    );
  }

  /// `Price starts from`
  String get Pricestartsfrom {
    return Intl.message(
      'Price starts from',
      name: 'Pricestartsfrom',
      desc: '',
      args: [],
    );
  }

  /// `Delete Property`
  String get DeleteProperty {
    return Intl.message(
      'Delete Property',
      name: 'DeleteProperty',
      desc: '',
      args: [],
    );
  }

  /// `Search Areas`
  String get SearchAreas {
    return Intl.message(
      'Search Areas',
      name: 'SearchAreas',
      desc: '',
      args: [],
    );
  }

  /// `Add New Area`
  String get AddNewArea {
    return Intl.message('Add New Area', name: 'AddNewArea', desc: '', args: []);
  }

  /// `Current Password`
  String get passcur {
    return Intl.message(
      'Current Password',
      name: 'passcur',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this property,if yes you wont be able to see again`
  String get deletepro {
    return Intl.message(
      'Are you sure you want to delete this property,if yes you wont be able to see again',
      name: 'deletepro',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete property`
  String get yeDeleteproperty {
    return Intl.message(
      'yes,Delete property',
      name: 'yeDeleteproperty',
      desc: '',
      args: [],
    );
  }

  /// `Hotels`
  String get Hotels {
    return Intl.message('Hotels', name: 'Hotels', desc: '', args: []);
  }

  /// `All Hotels`
  String get AllHotels {
    return Intl.message('All Hotels', name: 'AllHotels', desc: '', args: []);
  }

  /// `Add new hotel`
  String get Addnewhotel {
    return Intl.message(
      'Add new hotel',
      name: 'Addnewhotel',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Hotel Name`
  String get hotelar {
    return Intl.message(
      'Arabic Hotel Name',
      name: 'hotelar',
      desc: '',
      args: [],
    );
  }

  /// `English Hotel Name`
  String get hotenen {
    return Intl.message(
      'English Hotel Name',
      name: 'hotenen',
      desc: '',
      args: [],
    );
  }

  /// `Hotel name`
  String get Hotelname {
    return Intl.message('Hotel name', name: 'Hotelname', desc: '', args: []);
  }

  /// `Price range per night`
  String get Pricerangepernight {
    return Intl.message(
      'Price range per night',
      name: 'Pricerangepernight',
      desc: '',
      args: [],
    );
  }

  /// `Price per night`
  String get Pricepernight {
    return Intl.message(
      'Price per night',
      name: 'Pricepernight',
      desc: '',
      args: [],
    );
  }

  /// `Delete hotel`
  String get Deletehotel {
    return Intl.message(
      'Delete hotel',
      name: 'Deletehotel',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this hotel,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethishotelifyesyouwontbeabletoseeagain {
    return Intl.message(
      'Are you sure you want to delete this hotel,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethishotelifyesyouwontbeabletoseeagain',
      desc: '',
      args: [],
    );
  }

  /// `Yes,Delete hotel`
  String get YesDeletehotel {
    return Intl.message(
      'Yes,Delete hotel',
      name: 'YesDeletehotel',
      desc: '',
      args: [],
    );
  }

  /// `Resturants`
  String get Resturants {
    return Intl.message('Resturants', name: 'Resturants', desc: '', args: []);
  }

  /// `All resturants`
  String get Allresturants {
    return Intl.message(
      'All resturants',
      name: 'Allresturants',
      desc: '',
      args: [],
    );
  }

  /// `Add new resturant`
  String get Addnewresturant {
    return Intl.message(
      'Add new resturant',
      name: 'Addnewresturant',
      desc: '',
      args: [],
    );
  }

  /// `Resturant name`
  String get Resturantname {
    return Intl.message(
      'Resturant name',
      name: 'Resturantname',
      desc: '',
      args: [],
    );
  }

  /// `Average price(per person)`
  String get Averagepriceper {
    return Intl.message(
      'Average price(per person)',
      name: 'Averagepriceper',
      desc: '',
      args: [],
    );
  }

  /// `Delete Resturant`
  String get DeleteResturant {
    return Intl.message(
      'Delete Resturant',
      name: 'DeleteResturant',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this resturant,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethisresturant {
    return Intl.message(
      'Are you sure you want to delete this resturant,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethisresturant',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete resturant`
  String get yesDeleteresturant {
    return Intl.message(
      'yes,Delete resturant',
      name: 'yesDeleteresturant',
      desc: '',
      args: [],
    );
  }

  /// `Actvities`
  String get Actvities {
    return Intl.message('Actvities', name: 'Actvities', desc: '', args: []);
  }

  /// `All activities`
  String get Allactivities {
    return Intl.message(
      'All activities',
      name: 'Allactivities',
      desc: '',
      args: [],
    );
  }

  /// `Add New Activity`
  String get AddNewActivity {
    return Intl.message(
      'Add New Activity',
      name: 'AddNewActivity',
      desc: '',
      args: [],
    );
  }

  /// `Activity Name`
  String get ActivityName {
    return Intl.message(
      'Activity Name',
      name: 'ActivityName',
      desc: '',
      args: [],
    );
  }

  /// `Add New Car Rental`
  String get addcar {
    return Intl.message(
      'Add New Car Rental',
      name: 'addcar',
      desc: '',
      args: [],
    );
  }

  /// `Location information`
  String get Locationation {
    return Intl.message(
      'Location information',
      name: 'Locationation',
      desc: '',
      args: [],
    );
  }

  /// `Delete Car Rental`
  String get delecar {
    return Intl.message(
      'Delete Car Rental',
      name: 'delecar',
      desc: '',
      args: [],
    );
  }

  /// `Delete Activity`
  String get DeleteActivity {
    return Intl.message(
      'Delete Activity',
      name: 'DeleteActivity',
      desc: '',
      args: [],
    );
  }

  /// `Yes, Delete`
  String get yesde {
    return Intl.message('Yes, Delete', name: 'yesde', desc: '', args: []);
  }

  /// `Yes, Delete Car Rent`
  String get yesdecar {
    return Intl.message(
      'Yes, Delete Car Rent',
      name: 'yesdecar',
      desc: '',
      args: [],
    );
  }

  /// `All Car Rentals`
  String get allcar {
    return Intl.message('All Car Rentals', name: 'allcar', desc: '', args: []);
  }

  /// `Car Rentals`
  String get carr {
    return Intl.message('Car Rentals', name: 'carr', desc: '', args: []);
  }

  /// `Are you sure you want to delete this Car Rental,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethisactivitcaroseeagain {
    return Intl.message(
      'Are you sure you want to delete this Car Rental,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethisactivitcaroseeagain',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this activity,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethisactivityifyesyouwontbeabletoseeagain {
    return Intl.message(
      'Are you sure you want to delete this activity,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethisactivityifyesyouwontbeabletoseeagain',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete activity`
  String get yesDeleteactivity {
    return Intl.message(
      'yes,Delete activity',
      name: 'yesDeleteactivity',
      desc: '',
      args: [],
    );
  }

  /// `Coffee Shops`
  String get Shops {
    return Intl.message('Coffee Shops', name: 'Shops', desc: '', args: []);
  }

  /// `All Coffee Shops`
  String get Allshops {
    return Intl.message(
      'All Coffee Shops',
      name: 'Allshops',
      desc: '',
      args: [],
    );
  }

  /// `Asking Price`
  String get AskingPrice {
    return Intl.message(
      'Asking Price',
      name: 'AskingPrice',
      desc: '',
      args: [],
    );
  }

  /// `Add new shop`
  String get Addnewshop {
    return Intl.message('Add new shop', name: 'Addnewshop', desc: '', args: []);
  }

  /// `Contact Us`
  String get contact {
    return Intl.message('Contact Us', name: 'contact', desc: '', args: []);
  }

  /// `Shop name`
  String get Shopname {
    return Intl.message('Shop name', name: 'Shopname', desc: '', args: []);
  }

  /// `Message`
  String get Message {
    return Intl.message('Message', name: 'Message', desc: '', args: []);
  }

  /// `Submit`
  String get Submit {
    return Intl.message('Submit', name: 'Submit', desc: '', args: []);
  }

  /// `Set location on map?`
  String get Setlocationonmap {
    return Intl.message(
      'Set location on map?',
      name: 'Setlocationonmap',
      desc: '',
      args: [],
    );
  }

  /// `Delete Shop`
  String get DeleteShop {
    return Intl.message('Delete Shop', name: 'DeleteShop', desc: '', args: []);
  }

  /// `Are you sure you want to delete this Area, if yes you won’t be able to see it again.`
  String get deleare {
    return Intl.message(
      'Are you sure you want to delete this Area, if yes you won’t be able to see it again.',
      name: 'deleare',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this Restaurant, if yes you won’t be able to see it again.`
  String get deletmsgres {
    return Intl.message(
      'Are you sure you want to delete this Restaurant, if yes you won’t be able to see it again.',
      name: 'deletmsgres',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this Shop,if yes you wont be able to see again`
  String get AreyousureyouwanttodeletethisShopifyesyouwontbeabletoseeagain {
    return Intl.message(
      'Are you sure you want to delete this Shop,if yes you wont be able to see again',
      name: 'AreyousureyouwanttodeletethisShopifyesyouwontbeabletoseeagain',
      desc: '',
      args: [],
    );
  }

  /// `yes,DeleteShop`
  String get yesDeleteShop {
    return Intl.message(
      'yes,DeleteShop',
      name: 'yesDeleteShop',
      desc: '',
      args: [],
    );
  }

  /// `Areas`
  String get Areas {
    return Intl.message('Areas', name: 'Areas', desc: '', args: []);
  }

  /// `All Areas`
  String get AllAreas {
    return Intl.message('All Areas', name: 'AllAreas', desc: '', args: []);
  }

  /// `Arabic Shop Name`
  String get shopar {
    return Intl.message('Arabic Shop Name', name: 'shopar', desc: '', args: []);
  }

  /// `English Shop Name`
  String get shopen {
    return Intl.message(
      'English Shop Name',
      name: 'shopen',
      desc: '',
      args: [],
    );
  }

  /// `Add new Area`
  String get AddnewArea {
    return Intl.message('Add new Area', name: 'AddnewArea', desc: '', args: []);
  }

  /// `All Restaurants`
  String get allres {
    return Intl.message('All Restaurants', name: 'allres', desc: '', args: []);
  }

  /// `Area name`
  String get Areaname {
    return Intl.message('Area name', name: 'Areaname', desc: '', args: []);
  }

  /// `Media Upload`
  String get MediaUpload {
    return Intl.message(
      'Media Upload',
      name: 'MediaUpload',
      desc: '',
      args: [],
    );
  }

  /// `Al malek fahed street`
  String get Almalekfahedstreet {
    return Intl.message(
      'Al malek fahed street',
      name: 'Almalekfahedstreet',
      desc: '',
      args: [],
    );
  }

  /// `Please i want a very clean and big car`
  String get Pleaseiwantaverycleanandbigcar {
    return Intl.message(
      'Please i want a very clean and big car',
      name: 'Pleaseiwantaverycleanandbigcar',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get Yes {
    return Intl.message('Yes', name: 'Yes', desc: '', args: []);
  }

  /// `Dubai city center`
  String get Dubaicitycenter {
    return Intl.message(
      'Dubai city center',
      name: 'Dubaicitycenter',
      desc: '',
      args: [],
    );
  }

  /// `users used promo code`
  String get usersusedpromocode {
    return Intl.message(
      'users used promo code',
      name: 'usersusedpromocode',
      desc: '',
      args: [],
    );
  }

  /// `choose category`
  String get choosecategory {
    return Intl.message(
      'choose category',
      name: 'choosecategory',
      desc: '',
      args: [],
    );
  }

  /// `added on`
  String get addedon {
    return Intl.message('added on', name: 'addedon', desc: '', args: []);
  }

  /// `setallfeatures`
  String get setallfeatures {
    return Intl.message(
      'setallfeatures',
      name: 'setallfeatures',
      desc: '',
      args: [],
    );
  }

  /// `Roof Top`
  String get RoofTop {
    return Intl.message('Roof Top', name: 'RoofTop', desc: '', args: []);
  }

  /// `Arabic Property Name`
  String get arapro {
    return Intl.message(
      'Arabic Property Name',
      name: 'arapro',
      desc: '',
      args: [],
    );
  }

  /// `English Property Name`
  String get enpro {
    return Intl.message(
      'English Property Name',
      name: 'enpro',
      desc: '',
      args: [],
    );
  }

  /// `Start size`
  String get startsize {
    return Intl.message('Start size', name: 'startsize', desc: '', args: []);
  }

  /// `End size`
  String get endsize {
    return Intl.message('End size', name: 'endsize', desc: '', args: []);
  }

  /// `Arabic Restaurant Name`
  String get resara {
    return Intl.message(
      'Arabic Restaurant Name',
      name: 'resara',
      desc: '',
      args: [],
    );
  }

  /// `English Restaurant Name`
  String get resen {
    return Intl.message(
      'English Restaurant Name',
      name: 'resen',
      desc: '',
      args: [],
    );
  }

  /// `Average price`
  String get Averagep {
    return Intl.message('Average price', name: 'Averagep', desc: '', args: []);
  }

  /// `Delete Restaurant`
  String get deleres {
    return Intl.message(
      'Delete Restaurant',
      name: 'deleres',
      desc: '',
      args: [],
    );
  }

  /// `Add Caterogry feature`
  String get AddCaterogryfeature {
    return Intl.message(
      'Add Caterogry feature',
      name: 'AddCaterogryfeature',
      desc: '',
      args: [],
    );
  }

  /// `Remove Application feature`
  String get RemoveApplicationfeature {
    return Intl.message(
      'Remove Application feature',
      name: 'RemoveApplicationfeature',
      desc: '',
      args: [],
    );
  }

  /// `There are no items`
  String get Therearenoitems {
    return Intl.message(
      'There are no items',
      name: 'Therearenoitems',
      desc: '',
      args: [],
    );
  }

  /// `are you sure you want to delete this image`
  String get areyousureyouwanttodeletethisimage {
    return Intl.message(
      'are you sure you want to delete this image',
      name: 'areyousureyouwanttodeletethisimage',
      desc: '',
      args: [],
    );
  }

  /// `delete image`
  String get deleteimage {
    return Intl.message(
      'delete image',
      name: 'deleteimage',
      desc: '',
      args: [],
    );
  }

  /// `configuration has been updated successfully`
  String get configurationhasbeenupdatedsuccessfully {
    return Intl.message(
      'configuration has been updated successfully',
      name: 'configurationhasbeenupdatedsuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Google Review Name`
  String get GoogleReviewName {
    return Intl.message(
      'Google Review Name',
      name: 'GoogleReviewName',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get Title {
    return Intl.message('Title', name: 'Title', desc: '', args: []);
  }

  /// `is Published`
  String get isPublished {
    return Intl.message(
      'is Published',
      name: 'isPublished',
      desc: '',
      args: [],
    );
  }

  /// `Publish Date`
  String get publishDate {
    return Intl.message(
      'Publish Date',
      name: 'publishDate',
      desc: '',
      args: [],
    );
  }

  /// `cannot Delete Because It Is Used In Property`
  String get cannotDeleteBecauseItIsUsedInVideo {
    return Intl.message(
      'cannot Delete Because It Is Used In Property',
      name: 'cannotDeleteBecauseItIsUsedInVideo',
      desc: '',
      args: [],
    );
  }

  /// `English Title`
  String get TitleEn {
    return Intl.message('English Title', name: 'TitleEn', desc: '', args: []);
  }

  /// `Arabic Title`
  String get TitleAr {
    return Intl.message('Arabic Title', name: 'TitleAr', desc: '', args: []);
  }

  /// `English Description`
  String get descriptionEn {
    return Intl.message(
      'English Description',
      name: 'descriptionEn',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Description`
  String get descriptionAr {
    return Intl.message(
      'Arabic Description',
      name: 'descriptionAr',
      desc: '',
      args: [],
    );
  }

  /// `Car Years`
  String get CarYears {
    return Intl.message('Car Years', name: 'CarYears', desc: '', args: []);
  }

  /// `All Car Years`
  String get AllCarYears {
    return Intl.message(
      'All Car Years',
      name: 'AllCarYears',
      desc: '',
      args: [],
    );
  }

  /// `Add New Year`
  String get AddNewYear {
    return Intl.message('Add New Year', name: 'AddNewYear', desc: '', args: []);
  }

  /// `Edit Year`
  String get EditYear {
    return Intl.message('Edit Year', name: 'EditYear', desc: '', args: []);
  }

  /// `Year`
  String get Year {
    return Intl.message('Year', name: 'Year', desc: '', args: []);
  }

  /// `Delete Year`
  String get DeleteYear {
    return Intl.message('Delete Year', name: 'DeleteYear', desc: '', args: []);
  }

  /// `yes,Delete year`
  String get yesDeleteyear {
    return Intl.message(
      'yes,Delete year',
      name: 'yesDeleteyear',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this year,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethisyear {
    return Intl.message(
      'Are you sure you want to delete this year,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethisyear',
      desc: '',
      args: [],
    );
  }

  /// `Agreed Price`
  String get AgreedPrice {
    return Intl.message(
      'Agreed Price',
      name: 'AgreedPrice',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message('Start Date', name: 'startDate', desc: '', args: []);
  }

  /// `End Date`
  String get endDate {
    return Intl.message('End Date', name: 'endDate', desc: '', args: []);
  }

  /// `Price Details`
  String get PriceDetails {
    return Intl.message(
      'Price Details',
      name: 'PriceDetails',
      desc: '',
      args: [],
    );
  }

  /// `Normal Prices`
  String get NormalPrices {
    return Intl.message(
      'Normal Prices',
      name: 'NormalPrices',
      desc: '',
      args: [],
    );
  }

  /// `Schedule Days Prices`
  String get ScheduleDaysPrices {
    return Intl.message(
      'Schedule Days Prices',
      name: 'ScheduleDaysPrices',
      desc: '',
      args: [],
    );
  }

  /// `Show Schedule Prices`
  String get ShowSchedulePrices {
    return Intl.message(
      'Show Schedule Prices',
      name: 'ShowSchedulePrices',
      desc: '',
      args: [],
    );
  }

  /// `Normal Days`
  String get NormalDays {
    return Intl.message('Normal Days', name: 'NormalDays', desc: '', args: []);
  }

  /// `Day Price`
  String get DayPrice {
    return Intl.message('Day Price', name: 'DayPrice', desc: '', args: []);
  }

  /// `Night Price`
  String get NightPrice {
    return Intl.message('Night Price', name: 'NightPrice', desc: '', args: []);
  }

  /// `days`
  String get days {
    return Intl.message('days', name: 'days', desc: '', args: []);
  }

  /// `Arabic Website`
  String get websiteAr {
    return Intl.message(
      'Arabic Website',
      name: 'websiteAr',
      desc: '',
      args: [],
    );
  }

  /// `Users`
  String get Users {
    return Intl.message('Users', name: 'Users', desc: '', args: []);
  }

  /// `All Users`
  String get AllUsers {
    return Intl.message('All Users', name: 'AllUsers', desc: '', args: []);
  }

  /// `There are no users`
  String get ThereAreNoUsers {
    return Intl.message(
      'There are no users',
      name: 'ThereAreNoUsers',
      desc: '',
      args: [],
    );
  }

  /// `Android Users`
  String get androidUsers {
    return Intl.message(
      'Android Users',
      name: 'androidUsers',
      desc: '',
      args: [],
    );
  }

  /// `IOS Users`
  String get iosUsers {
    return Intl.message('IOS Users', name: 'iosUsers', desc: '', args: []);
  }

  /// `Add New Chalet`
  String get AddNewChalet {
    return Intl.message(
      'Add New Chalet',
      name: 'AddNewChalet',
      desc: '',
      args: [],
    );
  }

  /// `All chalets`
  String get Allchalets {
    return Intl.message('All chalets', name: 'Allchalets', desc: '', args: []);
  }

  /// `Chalets`
  String get Chalets {
    return Intl.message('Chalets', name: 'Chalets', desc: '', args: []);
  }

  /// `Arabic Chalet Name`
  String get chaletara {
    return Intl.message(
      'Arabic Chalet Name',
      name: 'chaletara',
      desc: '',
      args: [],
    );
  }

  /// `English Chalet Name`
  String get chaleteng {
    return Intl.message(
      'English Chalet Name',
      name: 'chaleteng',
      desc: '',
      args: [],
    );
  }

  /// `Delete Chalet`
  String get DeleteChalet {
    return Intl.message(
      'Delete Chalet',
      name: 'DeleteChalet',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this chalet,if yes you wont be able to see again`
  String get Areyousureyouwanttodeletethischaletifyesyouwontbeabletoseeagain {
    return Intl.message(
      'Are you sure you want to delete this chalet,if yes you wont be able to see again',
      name: 'Areyousureyouwanttodeletethischaletifyesyouwontbeabletoseeagain',
      desc: '',
      args: [],
    );
  }

  /// `yes,Delete chalet`
  String get yesDeletechalet {
    return Intl.message(
      'yes,Delete chalet',
      name: 'yesDeletechalet',
      desc: '',
      args: [],
    );
  }

  /// `RMS Properties`
  String get rmsProperties {
    return Intl.message(
      'RMS Properties',
      name: 'rmsProperties',
      desc: '',
      args: [],
    );
  }

  /// `Units`
  String get rmsUnits {
    return Intl.message('Units', name: 'rmsUnits', desc: '', args: []);
  }

  /// `Full Name Arabic`
  String get FullnameAr {
    return Intl.message(
      'Full Name Arabic',
      name: 'FullnameAr',
      desc: '',
      args: [],
    );
  }

  /// `Fill RMS Fields above please`
  String get fillRMSFields {
    return Intl.message(
      'Fill RMS Fields above please',
      name: 'fillRMSFields',
      desc: '',
      args: [],
    );
  }

  /// `Select RMS Unit`
  String get selectRmsUnit {
    return Intl.message(
      'Select RMS Unit',
      name: 'selectRmsUnit',
      desc: '',
      args: [],
    );
  }

  /// `Select RMS Property`
  String get selectRmsProperty {
    return Intl.message(
      'Select RMS Property',
      name: 'selectRmsProperty',
      desc: '',
      args: [],
    );
  }

  /// `Search RMS Property`
  String get searchRmsProperty {
    return Intl.message(
      'Search RMS Property',
      name: 'searchRmsProperty',
      desc: '',
      args: [],
    );
  }

  /// `Search RMS Unit`
  String get searchRmsUnit {
    return Intl.message(
      'Search RMS Unit',
      name: 'searchRmsUnit',
      desc: '',
      args: [],
    );
  }

  /// `No Result Found`
  String get noResultFound {
    return Intl.message(
      'No Result Found',
      name: 'noResultFound',
      desc: '',
      args: [],
    );
  }

  /// `Select RMS Area`
  String get selectRmsArea {
    return Intl.message(
      'Select RMS Area',
      name: 'selectRmsArea',
      desc: '',
      args: [],
    );
  }

  /// `Search Area`
  String get searchRmsArea {
    return Intl.message(
      'Search Area',
      name: 'searchRmsArea',
      desc: '',
      args: [],
    );
  }

  /// `Choose Categories`
  String get ChooseCategories {
    return Intl.message(
      'Choose Categories',
      name: 'ChooseCategories',
      desc: '',
      args: [],
    );
  }

  /// `Delete Price Plan`
  String get deletePricePlan {
    return Intl.message(
      'Delete Price Plan',
      name: 'deletePricePlan',
      desc: '',
      args: [],
    );
  }

  /// `Cannot delete because it is being used`
  String get cannotDeleteBecauseUsed {
    return Intl.message(
      'Cannot delete because it is being used',
      name: 'cannotDeleteBecauseUsed',
      desc: '',
      args: [],
    );
  }

  /// `Payment Plan`
  String get paymentPlan {
    return Intl.message(
      'Payment Plan',
      name: 'paymentPlan',
      desc: '',
      args: [],
    );
  }

  /// `Date`
  String get date {
    return Intl.message('Date', name: 'date', desc: '', args: []);
  }

  /// `Enter date`
  String get enterDate {
    return Intl.message('Enter date', name: 'enterDate', desc: '', args: []);
  }

  /// `Order`
  String get order {
    return Intl.message('Order', name: 'order', desc: '', args: []);
  }

  /// `Enter order`
  String get enterOrder {
    return Intl.message('Enter order', name: 'enterOrder', desc: '', args: []);
  }

  /// `Payment (%)`
  String get payment {
    return Intl.message('Payment (%)', name: 'payment', desc: '', args: []);
  }

  /// `Enter payment percentage`
  String get enterPaymentPercentage {
    return Intl.message(
      'Enter payment percentage',
      name: 'enterPaymentPercentage',
      desc: '',
      args: [],
    );
  }

  /// `Installment`
  String get installment {
    return Intl.message('Installment', name: 'installment', desc: '', args: []);
  }

  /// `Enter installment info`
  String get enterInstallmentInfo {
    return Intl.message(
      'Enter installment info',
      name: 'enterInstallmentInfo',
      desc: '',
      args: [],
    );
  }

  /// `Add Price Plan`
  String get addPricePlan {
    return Intl.message(
      'Add Price Plan',
      name: 'addPricePlan',
      desc: '',
      args: [],
    );
  }

  /// `Edit Price Plan`
  String get editPricePlan {
    return Intl.message(
      'Edit Price Plan',
      name: 'editPricePlan',
      desc: '',
      args: [],
    );
  }

  /// `Description (English)`
  String get descriptionEnglish {
    return Intl.message(
      'Description (English)',
      name: 'descriptionEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Enter description in English`
  String get enterDescriptionEnglish {
    return Intl.message(
      'Enter description in English',
      name: 'enterDescriptionEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Description (Arabic)`
  String get descriptionArabic {
    return Intl.message(
      'Description (Arabic)',
      name: 'descriptionArabic',
      desc: '',
      args: [],
    );
  }

  /// `Enter description in Arabic`
  String get enterDescriptionArabic {
    return Intl.message(
      'Enter description in Arabic',
      name: 'enterDescriptionArabic',
      desc: '',
      args: [],
    );
  }

  /// `Payment Plans`
  String get paymentPlans {
    return Intl.message(
      'Payment Plans',
      name: 'paymentPlans',
      desc: '',
      args: [],
    );
  }

  /// `Add More`
  String get addMore {
    return Intl.message('Add More', name: 'addMore', desc: '', args: []);
  }

  /// `Available Units`
  String get availableUnits {
    return Intl.message(
      'Available Units',
      name: 'availableUnits',
      desc: '',
      args: [],
    );
  }

  /// `Add Unit`
  String get addUnit {
    return Intl.message('Add Unit', name: 'addUnit', desc: '', args: []);
  }

  /// `Floor Plans`
  String get floorPlans {
    return Intl.message('Floor Plans', name: 'floorPlans', desc: '', args: []);
  }

  /// `Add Floor Plan`
  String get addFloorPlan {
    return Intl.message(
      'Add Floor Plan',
      name: 'addFloorPlan',
      desc: '',
      args: [],
    );
  }

  /// `Featured Settings`
  String get featuredSettings {
    return Intl.message(
      'Featured Settings',
      name: 'featuredSettings',
      desc: '',
      args: [],
    );
  }

  /// `Featured on Home`
  String get featuredOnHome {
    return Intl.message(
      'Featured on Home',
      name: 'featuredOnHome',
      desc: '',
      args: [],
    );
  }

  /// `Display this project on the home page`
  String get displayProjectOnHomePage {
    return Intl.message(
      'Display this project on the home page',
      name: 'displayProjectOnHomePage',
      desc: '',
      args: [],
    );
  }

  /// `Featured in Category`
  String get featuredInCategory {
    return Intl.message(
      'Featured in Category',
      name: 'featuredInCategory',
      desc: '',
      args: [],
    );
  }

  /// `Display this project prominently in its category`
  String get displayProjectInCategory {
    return Intl.message(
      'Display this project prominently in its category',
      name: 'displayProjectInCategory',
      desc: '',
      args: [],
    );
  }

  /// `Bedrooms (Arabic)`
  String get bedroomsArabic {
    return Intl.message(
      'Bedrooms (Arabic)',
      name: 'bedroomsArabic',
      desc: '',
      args: [],
    );
  }

  /// `Enter bedrooms in Arabic`
  String get enterBedroomsArabic {
    return Intl.message(
      'Enter bedrooms in Arabic',
      name: 'enterBedroomsArabic',
      desc: '',
      args: [],
    );
  }

  /// `Bedrooms (English)`
  String get bedroomsEnglish {
    return Intl.message(
      'Bedrooms (English)',
      name: 'bedroomsEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Enter bedrooms in English`
  String get enterBedroomsEnglish {
    return Intl.message(
      'Enter bedrooms in English',
      name: 'enterBedroomsEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Price From`
  String get priceFrom {
    return Intl.message('Price From', name: 'priceFrom', desc: '', args: []);
  }

  /// `Enter starting price`
  String get enterStartingPrice {
    return Intl.message(
      'Enter starting price',
      name: 'enterStartingPrice',
      desc: '',
      args: [],
    );
  }

  /// `Price To`
  String get priceTo {
    return Intl.message('Price To', name: 'priceTo', desc: '', args: []);
  }

  /// `Enter ending price`
  String get enterEndingPrice {
    return Intl.message(
      'Enter ending price',
      name: 'enterEndingPrice',
      desc: '',
      args: [],
    );
  }

  /// `Space Size (Arabic)`
  String get spaceSizeArabic {
    return Intl.message(
      'Space Size (Arabic)',
      name: 'spaceSizeArabic',
      desc: '',
      args: [],
    );
  }

  /// `Enter space size in Arabic`
  String get enterSpaceSizeArabic {
    return Intl.message(
      'Enter space size in Arabic',
      name: 'enterSpaceSizeArabic',
      desc: '',
      args: [],
    );
  }

  /// `Space Size (English)`
  String get spaceSizeEnglish {
    return Intl.message(
      'Space Size (English)',
      name: 'spaceSizeEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Enter space size in English`
  String get enterSpaceSizeEnglish {
    return Intl.message(
      'Enter space size in English',
      name: 'enterSpaceSizeEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Name (Arabic)`
  String get floorPlanNameArabic {
    return Intl.message(
      'Name (Arabic)',
      name: 'floorPlanNameArabic',
      desc: '',
      args: [],
    );
  }

  /// `Enter floor plan name in Arabic`
  String get enterFloorPlanNameArabic {
    return Intl.message(
      'Enter floor plan name in Arabic',
      name: 'enterFloorPlanNameArabic',
      desc: '',
      args: [],
    );
  }

  /// `Name (English)`
  String get floorPlanNameEnglish {
    return Intl.message(
      'Name (English)',
      name: 'floorPlanNameEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Enter floor plan name in English`
  String get enterFloorPlanNameEnglish {
    return Intl.message(
      'Enter floor plan name in English',
      name: 'enterFloorPlanNameEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Add Project`
  String get addProject {
    return Intl.message('Add Project', name: 'addProject', desc: '', args: []);
  }

  /// `Add New Project`
  String get addNewProject {
    return Intl.message(
      'Add New Project',
      name: 'addNewProject',
      desc: '',
      args: [],
    );
  }

  /// `Project added successfully`
  String get projectAddedSuccessfully {
    return Intl.message(
      'Project added successfully',
      name: 'projectAddedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add project`
  String get failedToAddProject {
    return Intl.message(
      'Failed to add project',
      name: 'failedToAddProject',
      desc: '',
      args: [],
    );
  }

  /// `Please enter Arabic name`
  String get pleaseEnterArabicName {
    return Intl.message(
      'Please enter Arabic name',
      name: 'pleaseEnterArabicName',
      desc: '',
      args: [],
    );
  }

  /// `Please enter English name`
  String get pleaseEnterEnglishName {
    return Intl.message(
      'Please enter English name',
      name: 'pleaseEnterEnglishName',
      desc: '',
      args: [],
    );
  }

  /// `Please enter Arabic description`
  String get pleaseEnterArabicDescription {
    return Intl.message(
      'Please enter Arabic description',
      name: 'pleaseEnterArabicDescription',
      desc: '',
      args: [],
    );
  }

  /// `Please enter English description`
  String get pleaseEnterEnglishDescription {
    return Intl.message(
      'Please enter English description',
      name: 'pleaseEnterEnglishDescription',
      desc: '',
      args: [],
    );
  }

  /// `Please select a type`
  String get pleaseSelectType {
    return Intl.message(
      'Please select a type',
      name: 'pleaseSelectType',
      desc: '',
      args: [],
    );
  }

  /// `Please select property status`
  String get pleaseSelectPropertyStatus {
    return Intl.message(
      'Please select property status',
      name: 'pleaseSelectPropertyStatus',
      desc: '',
      args: [],
    );
  }

  /// `Please select a price plan`
  String get pleaseSelectPricePlan {
    return Intl.message(
      'Please select a price plan',
      name: 'pleaseSelectPricePlan',
      desc: '',
      args: [],
    );
  }

  /// `Please select location`
  String get pleaseSelectLocation {
    return Intl.message(
      'Please select location',
      name: 'pleaseSelectLocation',
      desc: '',
      args: [],
    );
  }

  /// `Moving`
  String get moving {
    return Intl.message('Moving', name: 'moving', desc: '', args: []);
  }

  /// `Location selected`
  String get locationSelected {
    return Intl.message(
      'Location selected',
      name: 'locationSelected',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Name`
  String get arabicName {
    return Intl.message('Arabic Name', name: 'arabicName', desc: '', args: []);
  }

  /// `Enter Arabic Name`
  String get enterArabicName {
    return Intl.message(
      'Enter Arabic Name',
      name: 'enterArabicName',
      desc: '',
      args: [],
    );
  }

  /// `English Name`
  String get englishName {
    return Intl.message(
      'English Name',
      name: 'englishName',
      desc: '',
      args: [],
    );
  }

  /// `Enter English Name`
  String get enterEnglishName {
    return Intl.message(
      'Enter English Name',
      name: 'enterEnglishName',
      desc: '',
      args: [],
    );
  }

  /// `Floor Plan`
  String get floorPlan {
    return Intl.message('Floor Plan', name: 'floorPlan', desc: '', args: []);
  }

  /// `Unit`
  String get unit {
    return Intl.message('Unit', name: 'unit', desc: '', args: []);
  }

  /// `Floor Plan Image`
  String get floorPlanImage {
    return Intl.message(
      'Floor Plan Image',
      name: 'floorPlanImage',
      desc: '',
      args: [],
    );
  }

  /// `Select Location`
  String get selectLocation {
    return Intl.message(
      'Select Location',
      name: 'selectLocation',
      desc: '',
      args: [],
    );
  }

  /// `Property Status`
  String get propertyStatus {
    return Intl.message(
      'Property Status',
      name: 'propertyStatus',
      desc: '',
      args: [],
    );
  }

  /// `Price Plan`
  String get pricePlan {
    return Intl.message('Price Plan', name: 'pricePlan', desc: '', args: []);
  }

  /// `Gallery Images`
  String get galleryImages {
    return Intl.message(
      'Gallery Images',
      name: 'galleryImages',
      desc: '',
      args: [],
    );
  }

  /// `image selected`
  String get imageSelected {
    return Intl.message(
      'image selected',
      name: 'imageSelected',
      desc: '',
      args: [],
    );
  }

  /// `English Description`
  String get englishDescription {
    return Intl.message(
      'English Description',
      name: 'englishDescription',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Description`
  String get arabicDescription {
    return Intl.message(
      'Arabic Description',
      name: 'arabicDescription',
      desc: '',
      args: [],
    );
  }

  /// `images selected`
  String get imagesSelected {
    return Intl.message(
      'images selected',
      name: 'imagesSelected',
      desc: '',
      args: [],
    );
  }

  /// `Payment Method`
  String get paymentMethod {
    return Intl.message(
      'Payment Method',
      name: 'paymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Cash`
  String get cash {
    return Intl.message('Cash', name: 'cash', desc: '', args: []);
  }

  /// `Select Payment Method`
  String get selectPaymentMethod {
    return Intl.message(
      'Select Payment Method',
      name: 'selectPaymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Descriptions`
  String get descriptions {
    return Intl.message(
      'Descriptions',
      name: 'descriptions',
      desc: '',
      args: [],
    );
  }

  /// `Enter Arabic Description`
  String get enterArabicDescription {
    return Intl.message(
      'Enter Arabic Description',
      name: 'enterArabicDescription',
      desc: '',
      args: [],
    );
  }

  /// `Enter English Description`
  String get enterEnglishDescription {
    return Intl.message(
      'Enter English Description',
      name: 'enterEnglishDescription',
      desc: '',
      args: [],
    );
  }

  /// `Arabic description saved successfully`
  String get arabicDescriptionSaved {
    return Intl.message(
      'Arabic description saved successfully',
      name: 'arabicDescriptionSaved',
      desc: '',
      args: [],
    );
  }

  /// `English description saved successfully`
  String get englishDescriptionSaved {
    return Intl.message(
      'English description saved successfully',
      name: 'englishDescriptionSaved',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `RERA Permit`
  String get reraPermit {
    return Intl.message('RERA Permit', name: 'reraPermit', desc: '', args: []);
  }

  /// `RERA Number`
  String get reraNumber {
    return Intl.message('RERA Number', name: 'reraNumber', desc: '', args: []);
  }

  /// `RERA Permit Image`
  String get reraPermitImage {
    return Intl.message(
      'RERA Permit Image',
      name: 'reraPermitImage',
      desc: '',
      args: [],
    );
  }

  /// `Tap here to upload RERA permit image`
  String get tabHereToUploadReraPermitImage {
    return Intl.message(
      'Tap here to upload RERA permit image',
      name: 'tabHereToUploadReraPermitImage',
      desc: '',
      args: [],
    );
  }

  /// `RERA permit image selected`
  String get reraPermitImageSelected {
    return Intl.message(
      'RERA permit image selected',
      name: 'reraPermitImageSelected',
      desc: '',
      args: [],
    );
  }

  /// `Developers`
  String get developers {
    return Intl.message('Developers', name: 'developers', desc: '', args: []);
  }

  /// `Developer`
  String get developer {
    return Intl.message('Developer', name: 'developer', desc: '', args: []);
  }

  /// `Add New Developer`
  String get addNewDeveloper {
    return Intl.message(
      'Add New Developer',
      name: 'addNewDeveloper',
      desc: '',
      args: [],
    );
  }

  /// `All Developers`
  String get allDevelopers {
    return Intl.message(
      'All Developers',
      name: 'allDevelopers',
      desc: '',
      args: [],
    );
  }

  /// `Developer Details`
  String get developerDetails {
    return Intl.message(
      'Developer Details',
      name: 'developerDetails',
      desc: '',
      args: [],
    );
  }

  /// `Developer Name`
  String get developerName {
    return Intl.message(
      'Developer Name',
      name: 'developerName',
      desc: '',
      args: [],
    );
  }

  /// `Developer Name (Arabic)`
  String get developerNameAr {
    return Intl.message(
      'Developer Name (Arabic)',
      name: 'developerNameAr',
      desc: '',
      args: [],
    );
  }

  /// `Developer Name (English)`
  String get developerNameEn {
    return Intl.message(
      'Developer Name (English)',
      name: 'developerNameEn',
      desc: '',
      args: [],
    );
  }

  /// `Developer Description`
  String get developerDescription {
    return Intl.message(
      'Developer Description',
      name: 'developerDescription',
      desc: '',
      args: [],
    );
  }

  /// `Developer Description (Arabic)`
  String get developerDescriptionAr {
    return Intl.message(
      'Developer Description (Arabic)',
      name: 'developerDescriptionAr',
      desc: '',
      args: [],
    );
  }

  /// `Developer Description (English)`
  String get developerDescriptionEn {
    return Intl.message(
      'Developer Description (English)',
      name: 'developerDescriptionEn',
      desc: '',
      args: [],
    );
  }

  /// `Developer Logo`
  String get developerLogo {
    return Intl.message(
      'Developer Logo',
      name: 'developerLogo',
      desc: '',
      args: [],
    );
  }

  /// `Select Developer`
  String get selectDeveloper {
    return Intl.message(
      'Select Developer',
      name: 'selectDeveloper',
      desc: '',
      args: [],
    );
  }

  /// `Search Developers`
  String get searchDevelopers {
    return Intl.message(
      'Search Developers',
      name: 'searchDevelopers',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Delete`
  String get confirmDelete {
    return Intl.message(
      'Confirm Delete',
      name: 'confirmDelete',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this developer?`
  String get areYouSureDeleteDeveloper {
    return Intl.message(
      'Are you sure you want to delete this developer?',
      name: 'areYouSureDeleteDeveloper',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Developer deleted successfully`
  String get developerDeletedSuccessfully {
    return Intl.message(
      'Developer deleted successfully',
      name: 'developerDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to delete developer`
  String get failedToDeleteDeveloper {
    return Intl.message(
      'Failed to delete developer',
      name: 'failedToDeleteDeveloper',
      desc: '',
      args: [],
    );
  }

  /// `Projects`
  String get Projects {
    return Intl.message('Projects', name: 'Projects', desc: '', args: []);
  }

  /// `Search projects`
  String get SearchProjects {
    return Intl.message(
      'Search projects',
      name: 'SearchProjects',
      desc: '',
      args: [],
    );
  }

  /// `Error loading projects`
  String get ErrorLoadingProjects {
    return Intl.message(
      'Error loading projects',
      name: 'ErrorLoadingProjects',
      desc: '',
      args: [],
    );
  }

  /// `No projects found`
  String get NoProjectsFound {
    return Intl.message(
      'No projects found',
      name: 'NoProjectsFound',
      desc: '',
      args: [],
    );
  }

  /// `Delete Project`
  String get DeleteProject {
    return Intl.message(
      'Delete Project',
      name: 'DeleteProject',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this project?`
  String get AreYouSureYouWantToDeleteThisProject {
    return Intl.message(
      'Are you sure you want to delete this project?',
      name: 'AreYouSureYouWantToDeleteThisProject',
      desc: '',
      args: [],
    );
  }

  /// `Edit Project`
  String get EditProject {
    return Intl.message(
      'Edit Project',
      name: 'EditProject',
      desc: '',
      args: [],
    );
  }

  /// `Project updated successfully`
  String get projectUpdatedSuccessfully {
    return Intl.message(
      'Project updated successfully',
      name: 'projectUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Existing Images`
  String get existingImages {
    return Intl.message(
      'Existing Images',
      name: 'existingImages',
      desc: '',
      args: [],
    );
  }

  /// `Existing Main Video`
  String get existingMainVideo {
    return Intl.message(
      'Existing Main Video',
      name: 'existingMainVideo',
      desc: '',
      args: [],
    );
  }

  /// `Existing Main Video (Arabic)`
  String get existingMainVideoAr {
    return Intl.message(
      'Existing Main Video (Arabic)',
      name: 'existingMainVideoAr',
      desc: '',
      args: [],
    );
  }

  /// `Price From`
  String get PriceFrom {
    return Intl.message('Price From', name: 'PriceFrom', desc: '', args: []);
  }

  /// `Bedrooms`
  String get Bedrooms {
    return Intl.message('Bedrooms', name: 'Bedrooms', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
