{"version": "0.2.0", "configurations": [{"name": "idea2app_vendor", "request": "launch", "type": "dart"}, {"name": "idea2app_vendor (profile mode)", "request": "launch", "type": "dart", "flutterMode": "profile"}, {"name": "idea2app_vendor (release mode)", "request": "launch", "type": "dart", "flutterMode": "release"}, {"name": "idea2app_vendor (web)", "request": "launch", "type": "dart", "deviceId": "chrome", "flutterMode": "debug", "args": ["-d", "chrome", "--web-browser-flag", "--disable-web-security"]}, {"name": "idea2app_vendor (web profile)", "request": "launch", "type": "dart", "deviceId": "chrome", "flutterMode": "profile", "args": ["-d", "chrome", "--web-browser-flag", "--disable-web-security"]}, {"name": "idea2app_vendor (web release)", "request": "launch", "type": "dart", "deviceId": "chrome", "flutterMode": "release", "args": ["-d", "chrome", "--web-browser-flag", "--disable-web-security"]}, {"name": "dropdown_search", "cwd": "packages/dropdown_search", "request": "launch", "type": "dart"}, {"name": "dropdown_search (profile mode)", "cwd": "packages/dropdown_search", "request": "launch", "type": "dart", "flutterMode": "profile"}, {"name": "dropdown_search (release mode)", "cwd": "packages/dropdown_search", "request": "launch", "type": "dart", "flutterMode": "release"}, {"name": "rounded_loading_button", "cwd": "packages/rounded_loading_button", "request": "launch", "type": "dart"}, {"name": "rounded_loading_button (profile mode)", "cwd": "packages/rounded_loading_button", "request": "launch", "type": "dart", "flutterMode": "profile"}, {"name": "rounded_loading_button (release mode)", "cwd": "packages/rounded_loading_button", "request": "launch", "type": "dart", "flutterMode": "release"}, {"name": "xr_helper", "cwd": "packages/xr_helper", "request": "launch", "type": "dart"}, {"name": "xr_helper (profile mode)", "cwd": "packages/xr_helper", "request": "launch", "type": "dart", "flutterMode": "profile"}, {"name": "xr_helper (release mode)", "cwd": "packages/xr_helper", "request": "launch", "type": "dart", "flutterMode": "release"}]}